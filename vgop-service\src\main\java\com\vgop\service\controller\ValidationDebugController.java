package com.vgop.service.controller;

import com.vgop.service.common.ApiResponse;
import com.vgop.service.config.ValidationRulesProperties;
import com.vgop.service.validation.ValidationEngine;
import com.vgop.service.validation.ValidationRule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 校验规则调试控制器
 */
@Slf4j
@RestController
@RequestMapping("/debug/validation")
public class ValidationDebugController {

    @Autowired
    private ValidationRulesProperties validationRulesProperties;
    
    @Autowired
    private com.vgop.service.service.FileProcessingService fileProcessingService;
    
    @Autowired
    private com.vgop.service.config.VgopProperties vgopProperties;

    @Autowired
    private ValidationEngine validationEngine;

    @GetMapping("/interfaces")
    public ApiResponse<Set<String>> getAllInterfaces() {
        return ApiResponse.success(validationEngine.getAllInterfaces());
    }

    @GetMapping("/interfaces/{interfaceName}")
    public ApiResponse<List<ValidationRule>> getInterfaceRules(@PathVariable String interfaceName) {
        List<ValidationRule> rules = validationEngine.getRulesByInterface(interfaceName);
        if (rules.isEmpty()) {
            return ApiResponse.error(404, "未找到接口 " + interfaceName + " 的规则");
        }
        return ApiResponse.success(rules);
    }

    @GetMapping("/interfaces/{interfaceName}/fields")
    public ApiResponse<Set<String>> getInterfaceFields(@PathVariable String interfaceName) {
        Set<String> fields = validationEngine.getFieldsByInterface(interfaceName);
        if (fields.isEmpty()) {
            return ApiResponse.error(404, "未找到接口 " + interfaceName + " 或该接口下没有配置字段");
        }
        return ApiResponse.success(fields);
    }

    @GetMapping("/interfaces/{interfaceName}/fields/{fieldName}")
    public ApiResponse<List<ValidationRule>> getFieldRules(@PathVariable String interfaceName, @PathVariable String fieldName) {
        List<ValidationRule> rules = validationEngine.getRulesByField(interfaceName, fieldName);
        if (rules.isEmpty()) {
            return ApiResponse.error(404, String.format("未找到接口 %s 中字段 %s 的规则", interfaceName, fieldName));
        }
        return ApiResponse.success(rules);
    }

    /**
     * 测试路径生成逻辑
     * 用于验证路径拼接修复是否正确
     */
    @GetMapping("/test-path-generation")
    public ApiResponse<Map<String, String>> testPathGeneration(
            @RequestParam(defaultValue = "20091231") String dataDate,
            @RequestParam(defaultValue = "./") String imagePath) {
        
        Map<String, String> pathResults = new HashMap<>();
        
        try {
            // 1. 测试 generateOutputDirectory 方法
            String outputDir = fileProcessingService.generateOutputDirectory(imagePath, dataDate, "day");
            pathResults.put("outputDir", outputDir);
            
            // 2. 测试 Excel 报告路径生成
            String fileName = "不符合字段定义的数据记录." + dataDate + ".xlsx";
            String reportPath = com.vgop.service.util.FileUtil.PathUtil.joinPath(outputDir, fileName);
            pathResults.put("reportPath", reportPath);
            
            // 3. 测试原始路径拼接（修复前的逻辑）
            String oldStylePath = outputDir + "/" + fileName;
            pathResults.put("oldStylePath", oldStylePath);
            
            // 4. 测试路径规范化
            String normalizedPath = com.vgop.service.util.FileUtil.PathUtil.normalizePath(oldStylePath);
            pathResults.put("normalizedPath", normalizedPath);
            
            // 5. 测试各种路径组件
            pathResults.put("imagePath", imagePath);
            pathResults.put("dataRootPath", vgopProperties.getFileProcessing().getDataRootPath());
            pathResults.put("dataDate", dataDate);
            pathResults.put("subPath", "day");
            
            log.info("路径生成测试完成 - 修复前路径: {}, 修复后路径: {}", oldStylePath, reportPath);
            
            return ApiResponse.success(pathResults);
            
        } catch (Exception e) {
            log.error("路径生成测试失败", e);
            return ApiResponse.error(500, "路径生成测试失败: " + e.getMessage());
        }
    }
} 