# 开发环境配置
spring:
  # 数据源配置 - 开发环境使用GBase数据库
  datasource:
    # 主数据源 - GBase数据库 (BMS库)
    primary:
      driver-class-name: com.gbasedbt.jdbc.Driver
      url: jdbc:gbasedbt-sqli://10.1.33.207:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;
      username: ismp
      password: 1qaz@WSX
      # Druid连接池配置
      type: com.alibaba.druid.pool.DruidDataSource
      druid:
        initial-size: 5
        min-idle: 5
        max-active: 20
        max-wait: 60000
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        validation-query: SELECT 1 FROM systables WHERE tabid = 1
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 20
        filters: stat,wall
    
    # 次数据源配置
    secondary:
      driver-class-name: com.gbasedbt.jdbc.Driver
      url: jdbc:gbasedbt-sqli://10.1.33.207:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;
      username: ismp
      password: 1qaz@WSX
      # Druid连接池配置
      type: com.alibaba.druid.pool.DruidDataSource
      druid:
        initial-size: 3
        min-idle: 3
        max-active: 10
        max-wait: 60000
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        validation-query: SELECT 1 FROM systables WHERE tabid = 1
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 10
        filters: stat,wall

  # 移除H2控制台配置（不再使用H2）
  
  # SQL初始化配置
  sql:
    init:
      mode: never  # 开发环境不自动初始化数据库

# 开发环境日志级别
logging:
  level:
    com.vgop.service: DEBUG
    org.springframework.jdbc: DEBUG
    com.zaxxer.hikari: DEBUG

# 开发环境应用配置覆盖
app:
  # 调度配置
  schedules:
    daily-cron: "0 0 0 * * ?"  # 每天凌晨0点
    monthly-cron: "0 0 5 1 * ?" # 每月1日凌晨5点
#  schedules:
#     daily-cron: "0 */1 * * * ?"    # 日数据：每日凌晨0点
#     monthly-cron: "0 */1 * * * ?"  # 月数据：每月1日凌晨5点
  # 开发环境使用本地路径
  base-path:
    export-root: "./VGOPdata/datafile/"
    log-root: "./logs/"
    backup-root: "./data/backup/"
  
  # 开发环境SFTP配置（使用模拟服务）
  sftp:
    host: "***********"
    port: 19222
    username: "javadev1"
    password: "1qaz@WSX"
    remote-base-path: "./"
    connection-timeout: 5000
    retry-times: 1
    
  # DMZ文件中转配置（开发环境）
  dmz:
    # DMZ服务地址
    base-url: "http://***********"
    port: 8180
    # DMZ服务认证信息
    username: "vgop_user"
    password: "vgop_pass"
    # 文件清单通知接口路径
    file-list-notify-path: "/vgop/api/dmz/file-transfer/notify"
    # 连接配置
    connection-timeout: 30000
    read-timeout: 60000
    retry-times: 3
    # DMZ本地文件存储目录
    local-file-directory: "/home/<USER>/upload"
    # 下游SFTP配置（支持多个服务器）
    downstream-sftp-list:
      - name: "test-server"
        host: "***********"
        port: 19222
        username: "javadev1"
        password: "1qaz@WSX"
        remote-base-path: "/upload/"
        connection-timeout: 30000
        retry-times: 3
        enabled: true
    
  # 开发环境降低批处理数量，便于调试
  file-processing:
    max-rows-per-file: 10000  # 开发环境降低到1万行
    
  # 开发环境告警配置
  alert:
    storage:
      enable-file: true  # 开发环境同时输出到文件
      alert-file-path: "./data/alerts/"

  # 数据质量检测配置
  data-quality:
    # 启用数据质量检测
    enable-quality-check: true
    # 不合规数据占比阈值（默认0.1，即10%）
    non-compliance-threshold: 0.1
    # 数据量波动阈值（默认0.1，即10%）
    volume-fluctuation-threshold: 0.1
    # 生成不合规数据Excel报告
    generate-non-compliance-report: true
    # 不合规数据报告文件名模板
    non-compliance-report-template: "不符合字段定义的数据记录.{dataDate}.xlsx"
    # Excel报告最大行数限制
    max-report-rows: 10000

  tasks:
    # 日常任务列表
    daily:
      # 任务: 24201 - 按照VGOP1-R2.10-24201day.sh脚本配置的主号用户信息查询
      - interfaceId: "VGOP1-R2.10-24201"
        interfaceName: "VGOP1-R2.10-24201"
        enabled: true
        taskType: "daily"
        export:
          sqlTemplate: "SELECT mum.phonenumber AS phonenumber, mum.phonestate AS phonestate, mum.phoneimsi AS phoneimsi, mum.phoneimei AS phoneimei, mum.locationid AS locationid, SUBSTR(bp.bossid, 1, 3) AS provinceid, mum.openingtime AS openingtime, mum.Optime AS Optime, mum.sex AS sex FROM mcn_user_major mum LEFT JOIN bossprovince bp ON mum.provinceid = bp.provinceid WHERE mum.openingtime >= '{starttime}' AND mum.openingtime < '{endtime}' AND mum.phonestate IN ('0','1')"
          tempFileNameTemplate: "a_10000_{dataDate}_VGOP1-R2.10-24201.unl"
          outputFileNameTemplate: "a_10000_{dataDate}_VGOP1-R2.10-24201_{revTimes}_{fileNum}.dat"
          verfFileNameTemplate: "a_10000_{dataDate}_VGOP1-R2.10-24201_{revTimes}.verf"
          
      # 任务: 24202 - 按照VGOP1-R2.10-24202day.sh脚本配置的副号码明细查询
      - interfaceId: "VGOP1-R2.10-24202"
        interfaceName: "VGOP1-R2.10-24202"
        enabled: true
        taskType: "daily"
        export:
          # 修正SQL查询条件：参照脚本使用 openingtime<'{endtime}' （查询前一天数据）
          sqlTemplate: "select mcnnumber,phonenumber,business,shutdown,mcnimsi,mcnlocationid,numstate,mcnnature,mcnnum,channel, case channel when '9' then '3' else '1' end as mj,openingtime, Optime,mcimsitime,'0',Begintime,Endtime,ServID from mcn_user_minor where openingtime<'{endtime}' and phonenumber!='' and phonenumber is not null and mcnnum in (1,2,3)"
          tempFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.10-24202.unl"
          outputFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.10-24202_{revTimes}_{fileNum}.dat"
          verfFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.10-24202_{revTimes}.verf"
          # 文件分割配置：参照脚本每2000000行分割
          maxLinesPerFile: 2000000

      # 任务: 24203 - 按照VGOP1-R2.10-24203.sh脚本配置的用户行为日志查询
      - interfaceId: "VGOP1-R2.10-24203"
        interfaceName: "VGOP1-R2.10-24203"
        enabled: true
        taskType: "daily"
        export:
          # SQL查询：合并登录日志和操作日志，参照脚本逻辑
          sqlTemplate: "select '',phonenumber,type,optime,version from ( select phonenumber,1 as type,logintime as optime,version from mcn_apploginlog where logintime>='{starttime}' and logintime<'{endtime}' union all select phonenumber,2 as type,optime,'' as version from mcn_oplog where optime>='{starttime}' and optime<'{endtime}' and opmanner=4 )"
          tempFileNameTemplate: "a_10000_{dataDate}_VGOP1-R2.10-24203.unl"
          outputFileNameTemplate: "a_10000_{dataDate}_VGOP1-R2.10-24203_{revTimes}_{fileNum}.dat"
          verfFileNameTemplate: "a_10000_{dataDate}_VGOP1-R2.10-24203_{revTimes}.verf"
          # 文件分割配置：参照脚本每2000000行分割
          maxLinesPerFile: 2000000

      # 任务: 24205 - 按照VGOP1-R2.10-24205.sh脚本配置的通话话单查询
      - interfaceId: "VGOP1-R2.10-24205"
        interfaceName: "VGOP1-R2.10-24205"
        enabled: true
        taskType: "daily"
        export:
          # SQL查询：通话话单查询，参照脚本逻辑
          sqlTemplate: "select callType,callingPartyNumber,calledPartyNumber,mcnnumber,CallBeginTime,CallEndTime,CallDuration from Mcn_contralog where CallBeginTime>='{starttime}' and CallBeginTime<'{endtime}' and Cause != '80 81' and reason != '1' and length(callingPartyNumber) = 11 and length(calledPartyNumber) = 11 and callingPartyNumber like '1%' and calledPartyNumber like '1%'"
          tempFileNameTemplate: "a_10000_{dataDate}_VGOP1-R2.10-24205.unl"
          outputFileNameTemplate: "a_10000_{dataDate}_VGOP1-R2.10-24205_{revTimes}_{fileNum}.dat"
          verfFileNameTemplate: "a_10000_{dataDate}_VGOP1-R2.10-24205_{revTimes}.verf"
          # 文件分割配置：参照脚本每2000000行分割
          maxLinesPerFile: 2000000

      # 任务: 24206 - 按照VGOP1-R2.10-24206.sh脚本配置的短信日志查询
      - interfaceId: "VGOP1-R2.10-24206"
        interfaceName: "VGOP1-R2.10-24206"
        enabled: true
        taskType: "daily"
        export:
          # SQL查询：短信日志查询，参照脚本逻辑
          sqlTemplate: "select chargetype,phonenumber,mcnnumber,sendorreceNum,optime from mcn_smslog where optime>='{starttime}' and optime<'{endtime}' and length(phonenumber)='11' and length(sendorreceNum) = 11 and sendorreceNum like '1%'"
          tempFileNameTemplate: "a_10000_{dataDate}_VGOP1-R2.10-24206.unl"
          outputFileNameTemplate: "a_10000_{dataDate}_VGOP1-R2.10-24206_{revTimes}_{fileNum}.dat"
          verfFileNameTemplate: "a_10000_{dataDate}_VGOP1-R2.10-24206_{revTimes}.verf"
          # 文件分割配置：参照脚本每2000000行分割
          maxLinesPerFile: 2000000

      # 任务: 24207 - 按照VGOP1-R2.10-24207day.sh脚本配置的实体副号用户查询
      - interfaceId: "VGOP1-R2.10-24207"
        interfaceName: "VGOP1-R2.10-24207"
        enabled: true
        taskType: "daily"
        export:
          # SQL查询：实体副号用户查询，参照脚本逻辑UNION ALL两个表
          sqlTemplate: "select phonenumber,mcnnumber,mcnImsi,businessState,Numstate,Locationid,BossProvinceid,openingtime from ((select phonenumber,mcnnumber,mcnImsi,businessState,Numstate,Locationid,BossProvinceid,openingtime from mcn_sec_major where openingtime>='{starttime}' and openingtime<'{endtime}') union all (select phonenumber,mcnnumber,mcnImsi,businessState,Numstate,Locationid,BossProvinceid,openingtime from mcn_sec_major2 where openingtime>='{starttime}' and openingtime<'{endtime}'))"
          tempFileNameTemplate: "a_10000_{dataDate}_VGOP1-R2.10-24207.unl"
          outputFileNameTemplate: "a_10000_{dataDate}_VGOP1-R2.10-24207_{revTimes}_{fileNum}.dat"
          verfFileNameTemplate: "a_10000_{dataDate}_VGOP1-R2.10-24207_{revTimes}.verf"
          # 文件分割配置：参照脚本每2000000行分割
          maxLinesPerFile: 2000000

      # 任务: 24101 - 按照VGOP1-R2.11-24101.sh脚本配置的业务分析统计查询
      - interfaceId: "VGOP1-R2.11-24101"
        interfaceName: "VGOP1-R2.11-24101"
        enabled: true
        taskType: "daily"
        export:
          # SQL查询：业务分析统计查询，参照脚本逻辑，先调用存储过程再查询结果表
          sqlTemplate: "select provinceid,locationid,phonenum,mcnnum,appactivenum,mcnactivenum,paynum,feenum,secphonenum,secmcnnum,amcnnum from bms_vgop_banalyse where stattime='{previousDay}'"
          tempFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.11-24101.unl"
          outputFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.11-24101_{revTimes}_{fileNum}.dat"
          verfFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.11-24101_{revTimes}.verf"
          # 文件分割配置：参照脚本每2000000行分割
          maxLinesPerFile: 2000000

      # 任务: 24301 - 按照VGOP1-R2.13-24301.sh脚本配置的服务类型维表查询
      - interfaceId: "VGOP1-R2.13-24301"
        interfaceName: "VGOP1-R2.13-24301"
        enabled: true
        taskType: "daily"
        export:
          # SQL查询：服务类型维表查询，参照脚本逻辑，无时间过滤条件
          sqlTemplate: "select ServID,ServName from vgop_servtype"
          tempFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.13-24301.unl"
          outputFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.13-24301_{revTimes}_{fileNum}.dat"
          verfFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.13-24301_{revTimes}.verf"
          # 文件分割配置：参照脚本每2000000行分割
          maxLinesPerFile: 2000000

      # 任务: 24302 - 按照VGOP1-R2.13-24302.sh脚本配置的渠道信息维表查询
      - interfaceId: "VGOP1-R2.13-24302"
        interfaceName: "VGOP1-R2.13-24302"
        enabled: true
        taskType: "daily"
        export:
          # SQL查询：渠道信息维表查询，参照脚本逻辑，无时间过滤条件
          sqlTemplate: "select channelcode,channelname from vgop_channel"
          tempFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.13-24302.unl"
          outputFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.13-24302_{revTimes}_{fileNum}.dat"
          verfFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.13-24302_{revTimes}.verf"
          # 文件分割配置：参照脚本每2000000行分割
          maxLinesPerFile: 2000000

      # 任务: 24303 - 按照VGOP1-R2.13-24303.sh脚本配置的停机标识维表查询
      - interfaceId: "VGOP1-R2.13-24303"
        interfaceName: "VGOP1-R2.13-24303"
        enabled: true
        taskType: "daily"
        export:
          # SQL查询：停机标识维表查询，参照脚本逻辑，无时间过滤条件
          sqlTemplate: "select shutdown,shutdownname from vgop_shutdown"
          tempFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.13-24303.unl"
          outputFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.13-24303_{revTimes}_{fileNum}.dat"
          verfFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.13-24303_{revTimes}.verf"
          # 文件分割配置：参照脚本每2000000行分割
          maxLinesPerFile: 2000000

    # 月度任务列表
    monthly:
      # 任务: 24201 - 按照VGOP1-R2.10-24201month.sh脚本配置的主号用户信息月查询
      - interfaceId: "VGOP1-R2.10-24201"
        interfaceName: "VGOP1-R2.10-24201"
        enabled: true
        taskType: "monthly"
        export:
          # SQL查询：主号用户信息月度查询，参照脚本逻辑，查询截止到月初的历史数据
          sqlTemplate: "select mum.phonenumber as phonenumber,mum.phonestate as phonestate,mum.phoneimsi as phoneimsi,mum.phoneimei as phoneimei,mum.locationid as locationid,substr(bp.bossid, 1, 3) as provinceid,mum.openingtime as openingtime,mum.Optime as Optime,mum.sex as sex from mcn_user_major mum left join bossprovince bp on mum.provinceid = bp.provinceid where mum.openingtime<'{endtime}'"
          tempFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.10-24201.unl"
          outputFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.10-24201_{revTimes}_{fileNum}.dat"
          verfFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.10-24201_{revTimes}.verf"
          # 文件分割配置：参照脚本每2000000行分割
          maxLinesPerFile: 2000000
          
      # 任务: 24202 - 按照VGOP1-R2.10-24202month.sh脚本配置的副号码明细月查询
      - interfaceId: "VGOP1-R2.10-24202"
        interfaceName: "VGOP1-R2.10-24202"
        enabled: true
        taskType: "monthly"
        export:
          # SQL查询：副号码明细月度查询，参照脚本逻辑
          sqlTemplate: "select mcnnumber,phonenumber,business,shutdown,mcnimsi,mcnlocationid,numstate,mcnnature,mcnnum,channel, case channel when '9' then '3' else '1' end as mj,openingtime, Optime,mcimsitime,'0',Begintime,Endtime,ServID from mcn_user_minor where openingtime<'{endtime}' and phonenumber!='' and phonenumber is not null and mcnnum in (1,2,3)"
          tempFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.10-24202.unl"
          outputFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.10-24202_{revTimes}_{fileNum}.dat"
          verfFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.10-24202_{revTimes}.verf"
          # 文件分割配置：参照脚本每2000000行分割
          maxLinesPerFile: 2000000

      # 任务: 24204 - 按照VGOP1-R2.10-24204.sh脚本配置的用户开销户操作日志月查询
      - interfaceId: "VGOP1-R2.10-24204"
        interfaceName: "VGOP1-R2.10-24204"
        enabled: true
        taskType: "monthly"
        export:
          # SQL查询：用户开销户操作日志月度查询，参照脚本逻辑
          sqlTemplate: "select '{dataDate}',phonenumber,case optype when 's' then '01' when 'z' then '02' end as optype,optime,'' from mcn_oplog where optime<'{endtime}' and optime>='{starttime}' and opmanner=9 and (optype='s' or optype='z') and phonenumber!='' and phonenumber is not null"
          tempFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.10-24204.unl"
          outputFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.10-24204_{revTimes}_{fileNum}.dat"
          verfFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.10-24204_{revTimes}.verf"
          # 文件分割配置：参照脚本每2000000行分割
          maxLinesPerFile: 2000000

      # 任务: 24207 - 按照VGOP1-R2.10-24207month.sh脚本配置的实体副号用户月查询
      - interfaceId: "VGOP1-R2.10-24207"
        interfaceName: "VGOP1-R2.10-24207"
        enabled: true
        taskType: "monthly"
        export:
          # SQL查询：实体副号用户月度查询，参照脚本逻辑UNION ALL两个表，业务状态为'X'
          sqlTemplate: "select phonenumber,mcnnumber,mcnImsi,businessState,Numstate,Locationid,BossProvinceid,openingtime from ((select phonenumber,mcnnumber,mcnImsi,businessState,Numstate,Locationid,BossProvinceid,openingtime from mcn_sec_major where openingtime<'{endtime}' and businessState='X') union all (select phonenumber,mcnnumber,mcnImsi,businessState,Numstate,Locationid,BossProvinceid,openingtime from mcn_sec_major2 where openingtime<'{endtime}' and businessState='X'))"
          tempFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.10-24207.unl"
          outputFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.10-24207_{revTimes}_{fileNum}.dat"
          verfFileNameTemplate: "i_10000_{dataDate}_VGOP1-R2.10-24207_{revTimes}.verf"
          # 文件分割配置：参照脚本每2000000行分割
          maxLinesPerFile: 2000000

# 开发环境启用所有actuator端点
management:
  endpoints:
    web:
      exposure:
        include: "*"

# VGOP数据校验规则配置
validation:
  interfaces:
    'VGOP1-R2-10-24201':  # 使用引号将接口名作为单个键
      delimiter: "\\|"  # 使用|作为字段分隔符
      headerLine: false   # 没有表头行
      fields:
        # 第1位：用户主号号码（手机号）- 不带"+86"的11位数字号码，兼容国际号码加字冠00
        - fieldIndex: 0
          fieldName: "phonenumber"
          description: "用户主号号码"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24201.phonenumber.format"
              ruleName: "手机号格式校验"
              type: "FORMAT"
              pattern: "^(1[3-9]\\d{9}|00\\d{11,13}|)$"
              enabled: true
              severity: "WARNING"
              message: "手机号格式不正确，应为不带+86的11位数字号码或兼容国际号码加字冠00"
            - ruleId: "VGOP1-R2.10-24201.phonenumber.length"
              ruleName: "手机号码长度校验"
              type: "LENGTH"
              max: 14
              enabled: true
              severity: "WARNING"
              message: "手机号码长度不能超过14位"
        
        # 第2位：主号码业务状态 - 0正常，1暂停
        - fieldIndex: 1
          fieldName: "phonestate"
          description: "主号码业务状态"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24201.phonestate.enum"
              ruleName: "业务状态枚举值校验"
              type: "ENUM"
              values: ["0", "1", ""]
              enabled: true
              severity: "WARNING"
              message: "业务状态应为：0(正常)、1(暂停)或空值"
        
        # 第3位：主号码的IMSI号码
        - fieldIndex: 2
          fieldName: "phoneimsi"
          description: "主号码的IMSI号码"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24201.phoneimsi.length"
              ruleName: "IMSI长度校验"
              type: "LENGTH"
              max: 16
              enabled: true
              severity: "WARNING"
              message: "IMSI号码长度不能超过16位"
        
        # 第4位：主号码的IMEI号
        - fieldIndex: 3
          fieldName: "phoneimei"
          description: "主号码的IMEI号"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24201.phoneimei.length"
              ruleName: "IMEI长度校验"
              type: "LENGTH"
              max: 15
              enabled: true
              severity: "WARNING"
              message: "IMEI号码长度不能超过15位"
        
        # 第5位：主号码归属区 - 不带0
        - fieldIndex: 4
          fieldName: "locationid"
          description: "主号码归属区"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24201.locationid.length"
              ruleName: "归属区长度校验"
              type: "LENGTH"
              max: 5
              enabled: true
              severity: "WARNING"
              message: "归属区长度不能超过5位"
        
        # 第6位：主号码省ID
        - fieldIndex: 5
          fieldName: "provinceid"
          description: "主号码省ID"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24201.provinceid.length"
              ruleName: "省ID长度校验"
              type: "LENGTH"
              max: 5
              enabled: true
              severity: "WARNING"
              message: "省ID长度不能超过5位"
        
        # 第7位：开户时间 - 格式YYYYMMDDHH24MISS
        - fieldIndex: 6
          fieldName: "openingtime"
          description: "开户时间"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24201.openingtime.format"
              ruleName: "开户时间格式校验"
              type: "FORMAT"
              pattern: "^(20\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])[0-5]\\d[0-5]\\d|)$"
              enabled: true
              severity: "WARNING"
              message: "开户时间格式应为YYYYMMDDHH24MISS（14位数字）或空值"
        
        # 第8位：操作时间 - 稽核使用
        - fieldIndex: 7
          fieldName: "Optime"
          description: "操作时间"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24201.optime.format"
              ruleName: "操作时间格式校验"
              type: "FORMAT"
              pattern: "^(20\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])[0-5]\\d[0-5]\\d|)$"
              enabled: true
              severity: "WARNING"
              message: "操作时间格式应为YYYYMMDDHH24MISS（14位数字）或空值"
        
        # 第9位：网站用户头像性别 - 0男，1女
        - fieldIndex: 8
          fieldName: "sex"
          description: "网站用户头像性别"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24201.sex.enum"
              ruleName: "性别枚举值校验"
              type: "ENUM"
              values: ["0", "1", ""]
              enabled: true
              severity: "WARNING"
              message: "性别应为：0(男)、1(女)或空值"
    'VGOP1-R2-10-24202':
      delimiter: "\\|"
      headerLine: false
      fields:
        # 第1位：用户副号号码 - 手机号 VARCHAR2(11)
        - fieldIndex: 0
          fieldName: "mcnnumber"
          description: "用户副号号码"
          type: "MOBILE"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202.mcnnumber.format"
              ruleName: "副号码格式校验"
              type: "FORMAT"
              pattern: "^(1[3-9]\\d{9}|)$"
              enabled: true
              severity: "WARNING"
              message: "副号码格式不正确，应为11位手机号码，以1开头"
            - ruleId: "VGOP1-R2.10-24202.mcnnumber.length"
              ruleName: "副号码长度校验"
              type: "LENGTH"
              exact: 11
              enabled: true
              severity: "WARNING"
              message: "副号码长度必须为11位"
              
        # 第2位：用户主号号码 - 手机号 VARCHAR2(14)
        - fieldIndex: 1
          fieldName: "phonenumber"
          description: "用户主号号码"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202.phonenumber.format"
              ruleName: "主号码格式校验"
              type: "FORMAT"
              pattern: "^(1[3-9]\\d{9}|00\\d{11,13}|)$"
              enabled: true
              severity: "WARNING"
              message: "主号码格式不正确，应为不带+86的11位数字号码或兼容国际号码加字冠00"
            - ruleId: "VGOP1-R2.10-24202.phonenumber.length"
              ruleName: "主号码长度校验"
              type: "LENGTH"
              max: 14
              enabled: true
              severity: "WARNING"
              message: "主号码长度不能超过14位"
              
        # 第3位：副号码业务状态 - VARCHAR2(1)
        - fieldIndex: 2
          fieldName: "business"
          description: "副号码业务状态"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202.business.enum"
              ruleName: "业务状态枚举值校验"
              type: "ENUM"
              values: ["0", "1", "2", "3", "4", ""]
              enabled: true
              severity: "WARNING"
              message: "业务状态应为：0(正常)、1(预开户)、2(申请中)、3(取消中)、4(预验证)或空值"

              
        # 第4位：副号码停机标识 - VARCHAR2(2)
        - fieldIndex: 3
          fieldName: "shutdown"
          description: "副号码停机标识"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202.shutdown.length"
              ruleName: "停机标识长度校验"
              type: "LENGTH"
              max: 2
              enabled: true
              severity: "WARNING"
              message: "停机标识长度不能超过2位"
              
        # 第5位：副号码的IMSI号 - VARCHAR2(16)
        - fieldIndex: 4
          fieldName: "mcnimsi"
          description: "副号码的IMSI号"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202.mcnimsi.length"
              ruleName: "IMSI长度校验"
              type: "LENGTH"
              max: 16
              enabled: true
              severity: "WARNING"
              message: "IMSI号码长度不能超过16位"
            - ruleId: "VGOP1-R2.10-24202.mcnimsi.format"
              ruleName: "IMSI格式校验"
              type: "FORMAT"
              pattern: "^(\\d{15,16}|)$"
              enabled: true
              severity: "WARNING"
              message: "IMSI应为15-16位数字"
              
        # 第6位：副号码归属区号 - VARCHAR2(5)
        - fieldIndex: 5
          fieldName: "mcnlocationid"
          description: "副号码归属区号"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202.mcnlocationid.length"
              ruleName: "归属区号长度校验"
              type: "LENGTH"
              max: 5
              enabled: true
              severity: "WARNING"
              message: "归属区号长度不能超过5位，不带0"
              
        # 第7位：副号功能状态 - VARCHAR2(7)
        - fieldIndex: 6
          fieldName: "numstate"
          description: "副号功能状态"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202.numstate.length"
              ruleName: "功能状态长度校验"
              type: "LENGTH"
              exact: 7
              enabled: true
              severity: "WARNING"
              message: "功能状态长度应为7位，形如'1000000'"
              
        # 第8位：副号码类型 - VARCHAR2(1)
        - fieldIndex: 7
          fieldName: "mcnnature"
          description: "副号码类型"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202.mcnnature.enum"
              ruleName: "副号码类型枚举值校验"
              type: "ENUM"
              values: ["0", "1", ""]
              enabled: true
              severity: "WARNING"
              message: "副号码类型应为：0(虚拟副号码)、1(实体副号码)或空值"

              
        # 第9位：副号码序号 - VARCHAR2(2)
        - fieldIndex: 8
          fieldName: "mcnnum"
          description: "副号码序号"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202.mcnnum.enum"
              ruleName: "副号序号枚举值校验"
              type: "ENUM"
              values: ["1", "2", "3", ""]
              enabled: true
              severity: "ERROR"
              message: "副号序号应为：1(第一个副号)、2(第二个副号)、3(第三个副号)或空值"
            - ruleId: "VGOP1-R2.10-24202.mcnnum.length"
              ruleName: "副号序号长度校验"
              type: "LENGTH"
              max: 2
              enabled: true
              severity: "WARNING"
              message: "副号序号长度不能超过2位"
              
        # 第10位：副号码开户媒介 - VARCHAR2(2)
        - fieldIndex: 9
          fieldName: "channel"
          description: "副号码开户媒介"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202.channel.length"
              ruleName: "开户媒介长度校验"
              type: "LENGTH"
              max: 2
              enabled: true
              severity: "WARNING"
              message: "开户媒介长度不能超过2位"
              
        # 第11位：副号码开户渠道 - VARCHAR2(2)
        - fieldIndex: 10
          fieldName: "mj"
          description: "副号码开户渠道"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202.mj.enum"
              ruleName: "开户渠道枚举值校验"
              type: "ENUM"
              values: ["1", "2", "3", ""]
              enabled: true
              severity: "WARNING"
              message: "开户渠道应为：1(和多号平台自有渠道)、2(省公司渠道)、3(第三方渠道)或空值"

              
        # 第12位：开户时间 - VARCHAR2(14)
        - fieldIndex: 11
          fieldName: "openingtime"
          description: "开户时间"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202.openingtime.format"
              ruleName: "开户时间格式校验"
              type: "FORMAT"
              pattern: "^(20\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])[0-5]\\d[0-5]\\d|)$"
              enabled: true
              severity: "WARNING"
              message: "开户时间格式应为YYYYMMDDHH24MISS（14位数字）或空值"

              
        # 第13位：操作时间 - VARCHAR2(14)
        - fieldIndex: 12
          fieldName: "Optime"
          description: "操作时间"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202.optime.format"
              ruleName: "操作时间格式校验"
              type: "FORMAT"
              pattern: "^(20\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])[0-5]\\d[0-5]\\d|)$"
              enabled: true
              severity: "WARNING"
              message: "操作时间格式应为YYYYMMDDHH24MISS（14位数字）或空值"

              
        # 第14位：实体副号码IMSI号码获取时间 - VARCHAR2(14)
        - fieldIndex: 13
          fieldName: "mcimsitime"
          description: "实体副号码IMSI号码获取时间"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202.mcimsitime.format"
              ruleName: "IMSI获取时间格式校验"
              type: "FORMAT"
              pattern: "^(20\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])[0-5]\\d[0-5]\\d|)$"
              enabled: true
              severity: "WARNING"
              message: "IMSI获取时间格式应为YYYYMMDDHH24MISS（14位数字）或空值"

              
        # 第15位：用户类别 - VARCHAR2(2)
        - fieldIndex: 14
          fieldName: "usertype"
          description: "用户类别"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202.usertype.length"
              ruleName: "用户类别长度校验"
              type: "LENGTH"
              max: 2
              enabled: true
              severity: "WARNING"
              message: "用户类别长度不能超过2位"
              
        # 第16位：套餐生效时间 - VARCHAR2(14)
        - fieldIndex: 15
          fieldName: "Begintime"
          description: "套餐生效时间"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202.begintime.format"
              ruleName: "生效时间格式校验"
              type: "FORMAT"
              pattern: "^(20\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])[0-5]\\d[0-5]\\d|)$"
              enabled: true
              severity: "WARNING"
              message: "生效时间格式应为YYYYMMDDHH24MISS（14位数字）或空值"

              
        # 第17位：套餐失效时间 - VARCHAR2(14)
        - fieldIndex: 16
          fieldName: "Endtime"
          description: "套餐失效时间"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202.endtime.format"
              ruleName: "失效时间格式校验"
              type: "FORMAT"
              pattern: "^(20\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])[0-5]\\d[0-5]\\d|)$"
              enabled: true
              severity: "WARNING"
              message: "失效时间格式应为YYYYMMDDHH24MISS（14位数字）或空值"

              
        # 第18位：业务代码 - VARCHAR2(10)
        - fieldIndex: 17
          fieldName: "ServID"
          description: "业务代码"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202.servid.length"
              ruleName: "业务代码长度校验"
              type: "LENGTH"
              max: 10
              enabled: true
              severity: "WARNING"
              message: "业务代码长度不能超过10位"

      # 业务逻辑校验规则
      businessRules:
        # 校验副号码序号限制：参照脚本条件 mcnnum in (1,2,3)
        - ruleId: "VGOP1-R2.10-24202.business.mcnnum_constraint"
          ruleName: "副号码序号约束校验"
          description: "副号码序号必须在1、2、3范围内"
          condition: "mcnnum is not null and mcnnum != ''"
          assertion: "mcnnum in ['1', '2', '3']"
          enabled: true
          severity: "ERROR"
          message: "副号码序号必须为1、2或3"
          
        # 校验主号码非空约束：参照脚本条件 phonenumber!='' and phonenumber is not null
        - ruleId: "VGOP1-R2.10-24202.business.phonenumber_constraint"
          ruleName: "主号码非空约束校验"
          description: "主号码不能为空或null"
          condition: "true"
          assertion: "phonenumber is not null and phonenumber != ''"
          enabled: true
          severity: "ERROR"
          message: "主号码不能为空，这是脚本的必要条件"
          
        # 校验开户渠道计算逻辑：参照脚本 case channel when '9' then '3' else '1' end
        - ruleId: "VGOP1-R2.10-24202.business.channel_mj_logic"
          ruleName: "开户渠道计算逻辑校验"
          description: "开户渠道(mj)应根据开户媒介(channel)正确计算"
          condition: "channel is not null and channel != '' and mj is not null and mj != ''"
          assertion: "(channel == '9' and mj == '3') or (channel != '9' and mj == '1')"
          enabled: true
          severity: "WARNING"
          message: "开户渠道计算错误：channel为9时mj应为3，否则mj应为1"
          
        # 校验用户类别固定值：参照脚本固定值 '0'
        - ruleId: "VGOP1-R2.10-24202.business.usertype_fixed"
          ruleName: "用户类别固定值校验"
          description: "用户类别应为固定值0"
          condition: "usertype is not null and usertype != ''"
          assertion: "usertype == '0'"
          enabled: true
          severity: "WARNING"
          message: "用户类别应为固定值0，与脚本逻辑一致"
          
        # 校验时间格式一致性
        - ruleId: "VGOP1-R2.10-24202.business.time_format_consistency"
          ruleName: "时间格式一致性校验"
          description: "所有时间字段应使用统一的14位格式"
          condition: "openingtime is not null and openingtime != ''"
          assertion: "openingtime matches '^\\d{14}$'"
          enabled: true
          severity: "WARNING"
          message: "开户时间格式应为YYYYMMDDHH24MISS的14位数字格式"
          
        # 校验套餐时间逻辑：生效时间应早于失效时间
        - ruleId: "VGOP1-R2.10-24202.business.package_time_logic"
          ruleName: "套餐时间逻辑校验"
          description: "套餐生效时间应早于失效时间"
          condition: "Begintime is not null and Begintime != '' and Endtime is not null and Endtime != ''"
          assertion: "Begintime < Endtime"
          enabled: true
          severity: "WARNING"
          message: "套餐生效时间应早于失效时间"

    # 24203接口配置 - 用户行为日志
    'VGOP1-R2-10-24203':
      delimiter: "\\|"
      headerLine: false
      fields:
        # 第1位：流水帐号 - VARCHAR2(20) 可为空（脚本中固定为空字符串''）
        - fieldIndex: 0
          fieldName: "account_id"
          description: "流水帐号"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24203.account_id.length"
              ruleName: "流水帐号长度校验"
              type: "LENGTH"
              max: 20
              enabled: true
              severity: "WARNING"
              message: "流水帐号长度不能超过20位"
              
        # 第2位：用户主号号码 - 手机号 VARCHAR2(14)
        - fieldIndex: 1
          fieldName: "phonenumber"
          description: "用户主号号码"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24203.phonenumber.format"
              ruleName: "主号码格式校验"
              type: "FORMAT"
              pattern: "^(1[3-9]\\d{9}|00\\d{11,13}|)$"
              enabled: true
              severity: "WARNING"
              message: "主号码格式不正确，应为不带+86的11位数字号码或兼容国际号码加字冠00"
            - ruleId: "VGOP1-R2.10-24203.phonenumber.length"
              ruleName: "主号码长度校验"
              type: "LENGTH"
              max: 14
              enabled: true
              severity: "WARNING"
              message: "主号码长度不能超过14位"
              
        # 第3位：操作类型 - VARCHAR2(1)
        - fieldIndex: 2
          fieldName: "type"
          description: "操作类型"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24203.type.enum"
              ruleName: "操作类型枚举值校验"
              type: "ENUM"
              values: ["1", "2", ""]
              enabled: true
              severity: "WARNING"
              message: "操作类型应为：1(登录操作)、2(其他行为操作)或空值"
              
        # 第4位：操作时间 - VARCHAR2(14)
        - fieldIndex: 3
          fieldName: "optime"
          description: "操作时间"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24203.optime.format"
              ruleName: "操作时间格式校验"
              type: "FORMAT"
              pattern: "^(20\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])[0-5]\\d[0-5]\\d|)$"
              enabled: true
              severity: "WARNING"
              message: "操作时间格式应为YYYYMMDDHH24MISS（14位数字）或空值"

              
        # 第5位：客户端版本 - VARCHAR2(10)
        - fieldIndex: 4
          fieldName: "version"
          description: "客户端版本"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24203.version.length"
              ruleName: "客户端版本长度校验"
              type: "LENGTH"
              max: 10
              enabled: true
              severity: "WARNING"
              message: "客户端版本长度不能超过10位"

      # 业务逻辑校验规则
      businessRules:
        # 校验操作类型与版本的关系：参照脚本逻辑
        - ruleId: "VGOP1-R2.10-24203.business.type_version_logic"
          ruleName: "操作类型与版本关系校验"
          description: "登录操作(type=1)应有版本信息，其他操作(type=2)版本为空"
          condition: "type is not null and type != ''"
          assertion: "(type == '1' and version is not null and version != '') or (type == '2' and (version is null or version == ''))"
          enabled: true
          severity: "WARNING"
          message: "登录操作应有版本信息，其他操作版本应为空，与脚本逻辑一致"
          
        # 校验流水帐号固定值：参照脚本固定为空字符串
        - ruleId: "VGOP1-R2.10-24203.business.account_id_fixed"
          ruleName: "流水帐号固定值校验"
          description: "流水帐号在当前脚本中固定为空"
          condition: "true"
          assertion: "account_id is null or account_id == ''"
          enabled: true
          severity: "INFO"
          message: "流水帐号当前为空值，符合脚本逻辑"
          
        # 校验操作类型数据源一致性：参照脚本的UNION ALL逻辑
        - ruleId: "VGOP1-R2.10-24203.business.data_source_consistency"
          ruleName: "操作类型数据源一致性校验"
          description: "操作类型应与数据来源一致"
          condition: "type is not null and type != ''"
          assertion: "type in ['1', '2']"
          enabled: true
          severity: "ERROR"
          message: "操作类型必须为1(登录日志)或2(操作日志)，对应脚本中的两个数据源"
          
        # 校验时间范围合理性
        - ruleId: "VGOP1-R2.10-24203.business.time_range_validation"
          ruleName: "时间范围合理性校验"
          description: "操作时间应在合理范围内"
          condition: "optime is not null and optime != ''"
          assertion: "optime matches '^20\\d{12}$'"
          enabled: true
          severity: "WARNING"
          message: "操作时间应为21世纪的有效时间格式"

    # 24205接口配置 - 通话话单
    'VGOP1-R2-10-24205':
      delimiter: "\\|"
      headerLine: false
      fields:
        # 第1位：呼叫类型 - VARCHAR2(1)
        - fieldIndex: 0
          fieldName: "callType"
          description: "呼叫类型"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24205.calltype.enum"
              ruleName: "呼叫类型枚举值校验"
              type: "ENUM"
              values: ["0", "1", ""]
              enabled: true
              severity: "WARNING"
              message: "呼叫类型应为：0(主叫)、1(被叫)或空值"

              
        # 第2位：拨出号码 - VARCHAR2(24)
        - fieldIndex: 1
          fieldName: "callingPartyNumber"
          description: "拨出号码"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24205.callingpartynumber.format"
              ruleName: "拨出号码格式校验"
              type: "FORMAT"
              pattern: "^(1[3-9]\\d{9}|00\\d{11,22}|)$"
              enabled: true
              severity: "WARNING"
              message: "拨出号码格式不正确，应为不带+86的11位数字号码或兼容国际号码加字冠00"
            - ruleId: "VGOP1-R2.10-24205.callingpartynumber.length"
              ruleName: "拨出号码长度校验"
              type: "LENGTH"
              max: 24
              enabled: true
              severity: "WARNING"
              message: "拨出号码长度不能超过24位"

              
        # 第3位：接听号码 - VARCHAR2(24)
        - fieldIndex: 2
          fieldName: "calledPartyNumber"
          description: "接听号码"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24205.calledpartynumber.format"
              ruleName: "接听号码格式校验"
              type: "FORMAT"
              pattern: "^(1[3-9]\\d{9}|00\\d{11,22}|)$"
              enabled: true
              severity: "WARNING"
              message: "接听号码格式不正确，应为不带+86的11位数字号码或兼容国际号码加字冠00"
            - ruleId: "VGOP1-R2.10-24205.calledpartynumber.length"
              ruleName: "接听号码长度校验"
              type: "LENGTH"
              max: 24
              enabled: true
              severity: "WARNING"
              message: "接听号码长度不能超过24位"

              
        # 第4位：用户副号号码 - VARCHAR2(11)
        - fieldIndex: 3
          fieldName: "mcnnumber"
          description: "用户副号号码"
          type: "MOBILE"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24205.mcnnumber.format"
              ruleName: "副号码格式校验"
              type: "FORMAT"
              pattern: "^(1[3-9]\\d{9}|)$"
              enabled: true
              severity: "WARNING"
              message: "副号码格式不正确，应为不带+86的11位数字号码"

              
        # 第5位：通话开始时间 - VARCHAR2(14)
        - fieldIndex: 4
          fieldName: "CallBeginTime"
          description: "通话开始时间"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24205.callbegintime.format"
              ruleName: "通话开始时间格式校验"
              type: "FORMAT"
              pattern: "^(20\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])[0-5]\\d[0-5]\\d|)$"
              enabled: true
              severity: "WARNING"
              message: "通话开始时间格式应为YYYYMMDDHH24MISS（14位数字）或空值"

              
        # 第6位：通话结束时间 - VARCHAR2(14)
        - fieldIndex: 5
          fieldName: "CallEndTime"
          description: "通话结束时间"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24205.callendtime.format"
              ruleName: "通话结束时间格式校验"
              type: "FORMAT"
              pattern: "^(20\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])[0-5]\\d[0-5]\\d|)$"
              enabled: true
              severity: "WARNING"
              message: "通话结束时间格式应为YYYYMMDDHH24MISS（14位数字）或空值"

              
        # 第7位：通话时长 - NUMBER(12)
        - fieldIndex: 6
          fieldName: "CallDuration"
          description: "通话时长"
          type: "INTEGER"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24205.callduration.format"
              ruleName: "通话时长格式校验"
              type: "FORMAT"
              pattern: "^(\\d{1,12}|)$"
              enabled: true
              severity: "WARNING"
              message: "通话时长应为数字格式（最大12位）或空值"
            - ruleId: "VGOP1-R2.10-24205.callduration.range"
              ruleName: "通话时长范围校验"
              type: "RANGE"
              min: 0
              max: 2147483647
              enabled: true
              severity: "WARNING"
              message: "通话时长应在合理范围内（0-2147483647秒）"

      # 业务逻辑校验规则
      businessRules:
        # 校验时间逻辑：通话开始时间应早于结束时间
        - ruleId: "VGOP1-R2.10-24205.business.call_time_logic"
          ruleName: "通话时间逻辑校验"
          description: "通话开始时间应早于结束时间"
          condition: "CallBeginTime is not null and CallBeginTime != '' and CallEndTime is not null and CallEndTime != ''"
          assertion: "CallBeginTime < CallEndTime"
          enabled: true
          severity: "ERROR"
          message: "通话开始时间应早于结束时间"
          
        # 校验号码长度约束：参照脚本条件
        - ruleId: "VGOP1-R2.10-24205.business.phone_length_constraint"
          ruleName: "号码长度约束校验"
          description: "拨出号码和接听号码必须为11位"
          condition: "callingPartyNumber is not null and callingPartyNumber != '' and calledPartyNumber is not null and calledPartyNumber != ''"
          assertion: "length(callingPartyNumber) == 11 and length(calledPartyNumber) == 11"
          enabled: true
          severity: "ERROR"
          message: "拨出号码和接听号码长度必须为11位，符合脚本约束"
          
        # 校验号码格式约束：参照脚本条件 like '1%'
        - ruleId: "VGOP1-R2.10-24205.business.phone_format_constraint"
          ruleName: "号码格式约束校验"
          description: "拨出号码和接听号码必须以1开头"
          condition: "callingPartyNumber is not null and callingPartyNumber != '' and calledPartyNumber is not null and calledPartyNumber != ''"
          assertion: "callingPartyNumber startsWith '1' and calledPartyNumber startsWith '1'"
          enabled: true
          severity: "ERROR"
          message: "拨出号码和接听号码必须以1开头，符合脚本约束"
          
        # 校验通话时长合理性
        - ruleId: "VGOP1-R2.10-24205.business.duration_reasonableness"
          ruleName: "通话时长合理性校验"
          description: "通话时长应与时间差大致匹配"
          condition: "CallBeginTime is not null and CallBeginTime != '' and CallEndTime is not null and CallEndTime != '' and CallDuration is not null and CallDuration != ''"
          assertion: "CallDuration > 0"
          enabled: true
          severity: "WARNING"
          message: "通话时长应为正数"
          
        # 校验数据过滤条件：参照脚本过滤逻辑
        - ruleId: "VGOP1-R2.10-24205.business.filter_conditions"
          ruleName: "数据过滤条件校验"
          description: "数据应符合脚本的过滤条件"
          condition: "true"
          assertion: "true"
          enabled: true
          severity: "INFO"
          message: "数据已通过脚本过滤条件：Cause != '80 81' and reason != '1'"

    # 24206接口配置 - 短信日志
    'VGOP1-R2-10-24206':
      delimiter: "\\|"
      headerLine: false
      fields:
        # 第1位：话单类型 - VARCHAR2(1)
        - fieldIndex: 0
          fieldName: "chargetype"
          description: "话单类型"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24206.chargetype.enum"
              ruleName: "话单类型枚举值校验"
              type: "ENUM"
              values: ["0", "1", "2", "3", ""]
              enabled: true
              severity: "WARNING"
              message: "话单类型应为：0(使用副号发短信)、1(使用副号收短信)、2(使用副号接收彩信)、3(非业务用户使用业务接入码发送短信)或空值"

              
        # 第2位：用户主号号码 - VARCHAR2(14)
        - fieldIndex: 1
          fieldName: "phonenumber"
          description: "用户主号号码"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24206.phonenumber.format"
              ruleName: "主号码格式校验"
              type: "FORMAT"
              pattern: "^(1[3-9]\\d{9}|00\\d{11,13}|)$"
              enabled: true
              severity: "WARNING"
              message: "主号码格式不正确，应为不带+86的11位数字号码或兼容国际号码加字冠00"
            - ruleId: "VGOP1-R2.10-24206.phonenumber.length"
              ruleName: "主号码长度校验"
              type: "LENGTH"
              max: 14
              enabled: true
              severity: "WARNING"
              message: "主号码长度不能超过14位"

              
        # 第3位：用户副号号码 - VARCHAR2(11)
        - fieldIndex: 2
          fieldName: "mcnnumber"
          description: "用户副号号码"
          type: "MOBILE"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24206.mcnnumber.format"
              ruleName: "副号码格式校验"
              type: "FORMAT"
              pattern: "^(1[3-9]\\d{9}|)$"
              enabled: true
              severity: "WARNING"
              message: "副号码格式不正确，应为不带+86的11位数字号码"

              
        # 第4位：对端号码 - VARCHAR2(24)
        - fieldIndex: 3
          fieldName: "sendorreceNum"
          description: "对端号码"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24206.sendorrcenum.format"
              ruleName: "对端号码格式校验"
              type: "FORMAT"
              pattern: "^(1[3-9]\\d{9}|00\\d{11,22}|)$"
              enabled: true
              severity: "WARNING"
              message: "对端号码格式不正确，应为不带+86的11位数字号码或兼容国际号码加字冠00"
            - ruleId: "VGOP1-R2.10-24206.sendorrcenum.length"
              ruleName: "对端号码长度校验"
              type: "LENGTH"
              max: 24
              enabled: true
              severity: "WARNING"
              message: "对端号码长度不能超过24位"

              
        # 第5位：操作时间 - VARCHAR2(14)
        - fieldIndex: 4
          fieldName: "optime"
          description: "操作时间"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24206.optime.format"
              ruleName: "操作时间格式校验"
              type: "FORMAT"
              pattern: "^(20\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])[0-5]\\d[0-5]\\d|)$"
              enabled: true
              severity: "WARNING"
              message: "操作时间格式应为YYYYMMDDHH24MISS（14位数字）或空值"


      # 业务逻辑校验规则
      businessRules:
        # 校验主号码长度约束：参照脚本条件
        - ruleId: "VGOP1-R2.10-24206.business.phonenumber_length_constraint"
          ruleName: "主号码长度约束校验"
          description: "主号码必须为11位"
          condition: "phonenumber is not null and phonenumber != ''"
          assertion: "length(phonenumber) == 11"
          enabled: true
          severity: "ERROR"
          message: "主号码长度必须为11位，符合脚本约束"
          
        # 校验对端号码长度约束：参照脚本条件
        - ruleId: "VGOP1-R2.10-24206.business.sendorrcenum_length_constraint"
          ruleName: "对端号码长度约束校验"
          description: "对端号码必须为11位"
          condition: "sendorreceNum is not null and sendorreceNum != ''"
          assertion: "length(sendorreceNum) == 11"
          enabled: true
          severity: "ERROR"
          message: "对端号码长度必须为11位，符合脚本约束"
          
        # 校验对端号码格式约束：参照脚本条件 like '1%'
        - ruleId: "VGOP1-R2.10-24206.business.sendorrcenum_format_constraint"
          ruleName: "对端号码格式约束校验"
          description: "对端号码必须以1开头"
          condition: "sendorreceNum is not null and sendorreceNum != ''"
          assertion: "sendorreceNum startsWith '1'"
          enabled: true
          severity: "ERROR"
          message: "对端号码必须以1开头，符合脚本约束"
          
        # 校验话单类型与号码关系
        - ruleId: "VGOP1-R2.10-24206.business.chargetype_phonenumber_logic"
          ruleName: "话单类型与号码关系校验"
          description: "话单类型应与对应的号码字段保持逻辑一致"
          condition: "chargetype is not null and chargetype != ''"
          assertion: "true"
          enabled: true
          severity: "INFO"
          message: "话单类型说明：0(发短信-对端为被叫)、1(收短信-对端为主叫)、2(接收彩信)、3(非业务用户发短信)"
          
        # 校验操作时间范围合理性
        - ruleId: "VGOP1-R2.10-24206.business.optime_range"
          ruleName: "操作时间范围合理性校验"
          description: "操作时间应在合理的时间范围内"
          condition: "optime is not null and optime != ''"
          assertion: "optime matches '^20\\d{12}$'"
          enabled: true
          severity: "WARNING"
          message: "操作时间应为21世纪的有效时间格式"
          
        # 校验数据过滤条件：参照脚本过滤逻辑
        - ruleId: "VGOP1-R2.10-24206.business.filter_conditions"
          ruleName: "数据过滤条件校验"
          description: "数据应符合脚本的过滤条件"
          condition: "true"
          assertion: "true"
          enabled: true
          severity: "INFO"
          message: "数据已通过脚本过滤条件：length(phonenumber)='11' and length(sendorreceNum)=11 and sendorreceNum like '1%'"

    # 24207接口配置 - 实体副号用户
    'VGOP1-R2-10-24207':
      delimiter: "\\|"
      headerLine: false
      fields:
        # 第1位：用户主卡号码 - VARCHAR2(16)
        - fieldIndex: 0
          fieldName: "phonenumber"
          description: "用户主卡号码"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24207.phonenumber.format"
              ruleName: "主卡号码格式校验"
              type: "FORMAT"
              pattern: "^(1[3-9]\\d{9}|00\\d{11,13}|)$"
              enabled: true
              severity: "WARNING"
              message: "主卡号码格式不正确，应为不带+86的11位数字号码或兼容国际号码加字冠00"
            - ruleId: "VGOP1-R2.10-24207.phonenumber.length"
              ruleName: "主卡号码长度校验"
              type: "LENGTH"
              max: 16
              enabled: true
              severity: "WARNING"
              message: "主卡号码长度不能超过16位"
              
        # 第2位：用户副卡号码 - VARCHAR2(16)
        - fieldIndex: 1
          fieldName: "mcnnumber"
          description: "用户副卡号码"
          type: "MOBILE"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24207.mcnnumber.format"
              ruleName: "副卡号码格式校验"
              type: "FORMAT"
              pattern: "^(1[3-9]\\d{9}|00\\d{11,13}|)$"
              enabled: true
              severity: "WARNING"
              message: "副卡号码格式不正确，应为不带+86的11位数字号码或兼容国际号码加字冠00"
            - ruleId: "VGOP1-R2.10-24207.mcnnumber.length"
              ruleName: "副卡号码长度校验"
              type: "LENGTH"
              max: 16
              enabled: true
              severity: "WARNING"
              message: "副卡号码长度不能超过16位"
              
        # 第3位：副卡IMSI号 - VARCHAR2(20)
        - fieldIndex: 2
          fieldName: "mcnImsi"
          description: "副卡IMSI号"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24207.mcnimsi.format"
              ruleName: "IMSI格式校验"
              type: "FORMAT"
              pattern: "^(\\d{15,20}|)$"
              enabled: true
              severity: "WARNING"
              message: "IMSI应为15-20位数字"
            - ruleId: "VGOP1-R2.10-24207.mcnimsi.length"
              ruleName: "IMSI长度校验"
              type: "LENGTH"
              max: 20
              enabled: true
              severity: "WARNING"
              message: "IMSI长度不能超过20位"
              
        # 第4位：业务状态 - VARCHAR2(1)
        - fieldIndex: 3
          fieldName: "businessState"
          description: "业务状态"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24207.businessstate.enum"
              ruleName: "业务状态枚举值校验"
              type: "ENUM"
              values: ["0", "1", ""]
              enabled: true
              severity: "WARNING"
              message: "业务状态应为：0(正常)、1(暂停)或空值"
              
        # 第5位：副卡功能状态 - VARCHAR2(7)
        - fieldIndex: 4
          fieldName: "Numstate"
          description: "副卡功能状态"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24207.numstate.format"
              ruleName: "功能状态格式校验"
              type: "FORMAT"
              pattern: "^([01]{7}|)$"
              enabled: true
              severity: "WARNING"
              message: "功能状态应为7位二进制数字，第一位表示逻辑开关机状态"
              
        # 第6位：主号码归属区号 - VARCHAR2(5)
        - fieldIndex: 5
          fieldName: "Locationid"
          description: "主号码归属区号"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24207.locationid.length"
              ruleName: "归属区号长度校验"
              type: "LENGTH"
              max: 5
              enabled: true
              severity: "WARNING"
              message: "归属区号长度不能超过5位，不带0"
              
        # 第7位：主号码省ID - NUMBER(12)
        - fieldIndex: 6
          fieldName: "BossProvinceid"
          description: "主号码省ID"
          type: "INTEGER"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24207.bossprovinceid.format"
              ruleName: "省ID格式校验"
              type: "FORMAT"
              pattern: "^(\\d+|)$"
              enabled: true
              severity: "WARNING"
              message: "省ID应为数字格式，例如北京省ID为100"
            - ruleId: "VGOP1-R2.10-24207.bossprovinceid.range"
              ruleName: "省ID范围校验"
              type: "RANGE"
              min: 1
              max: 2147483647
              enabled: true
              severity: "WARNING"
              message: "省ID应在有效数字范围内"
              
        # 第8位：开户时间 - VARCHAR2(14)
        - fieldIndex: 7
          fieldName: "openingtime"
          description: "开户时间"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24207.openingtime.format"
              ruleName: "开户时间格式校验"
              type: "FORMAT"
              pattern: "^(20\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])[0-5]\\d[0-5]\\d|)$"
              enabled: true
              severity: "WARNING"
              message: "开户时间格式应为YYYYMMDDHH24MISS（14位数字）或空值"

      # 业务逻辑校验规则
      businessRules:
        # 校验数据源一致性：UNION ALL两个表的数据应该结构一致
        - ruleId: "VGOP1-R2.10-24207.business.data_source_consistency"
          ruleName: "数据源一致性校验"
          description: "来自mcn_sec_major和mcn_sec_major2的数据应该结构一致"
          condition: "true"
          assertion: "true"
          enabled: true
          severity: "INFO"
          message: "数据来源：mcn_sec_major和mcn_sec_major2表的UNION ALL"
          
        # 校验开户时间范围合理性
        - ruleId: "VGOP1-R2.10-24207.business.openingtime_range"
          ruleName: "开户时间范围合理性校验"
          description: "开户时间应在合理的时间范围内"
          condition: "openingtime is not null and openingtime != ''"
          assertion: "openingtime matches '^20\\d{12}$'"
          enabled: true
          severity: "WARNING"
          message: "开户时间应为21世纪的有效时间格式"
          
        # 校验主号码与副号码关系
        - ruleId: "VGOP1-R2.10-24207.business.phone_mcn_relationship"
          ruleName: "主号码与副号码关系校验"
          description: "主号码和副号码都不为空时应该保持业务关系"
          condition: "phonenumber is not null and phonenumber != '' and mcnnumber is not null and mcnnumber != ''"
          assertion: "phonenumber != mcnnumber"
          enabled: true
          severity: "WARNING"
          message: "主号码和副号码应该是不同的号码"
          
        # 校验IMSI与副号码关系
        - ruleId: "VGOP1-R2.10-24207.business.imsi_mcn_relationship"
          ruleName: "IMSI与副号码关系校验"
          description: "有副号码时通常应该有对应的IMSI"
          condition: "mcnnumber is not null and mcnnumber != ''"
          assertion: "true"
          enabled: true
          severity: "INFO"
          message: "实体副号码通常应该有对应的IMSI信息"
          
        # 校验业务状态合理性
        - ruleId: "VGOP1-R2.10-24207.business.business_state_reasonableness"
          ruleName: "业务状态合理性校验"
          description: "业务状态应与开户信息保持一致"
          condition: "businessState is not null and businessState != '' and openingtime is not null and openingtime != ''"
          assertion: "true"
          enabled: true
          severity: "INFO"
          message: "业务状态与开户时间应保持逻辑一致"
          
        # 校验数据时间范围约束：参照脚本过滤逻辑
        - ruleId: "VGOP1-R2.10-24207.business.time_range_constraint"
          ruleName: "数据时间范围约束校验"
          description: "数据应符合脚本的时间范围条件"
          condition: "true"
          assertion: "true"
          enabled: true
          severity: "INFO"
          message: "数据已通过脚本时间范围过滤：openingtime >= starttime and openingtime < endtime"

    # 24101接口配置 - 业务分析统计
    'VGOP1-R2-11-24101':
      delimiter: "\\|"
      headerLine: false
      fields:
        # 第3位：主号用户数（主号） - NUMBER(12)
        - fieldIndex: 2
          fieldName: "phonenum"
          description: "主号用户数（主号）"
          type: "INTEGER"
          required: false
          rules:
            - ruleId: "VGOP1-R2.11-24101.phonenum.format"
              ruleName: "主号用户数格式校验"
              type: "FORMAT"
              pattern: "^(\\d{1,12}|)$"
              enabled: true
              severity: "WARNING"
              message: "主号用户数应为数字格式（最大12位）"
            - ruleId: "VGOP1-R2.11-24101.phonenum.range"
              ruleName: "主号用户数范围校验"
              type: "RANGE"
              min: 0
              max: 2147483647
              enabled: true
              severity: "WARNING"
              message: "主号用户数应在合理范围内（0-2147483647）"
              
        # 第4位：副号用户数（副号） - NUMBER(12)
        - fieldIndex: 3
          fieldName: "mcnnum"
          description: "副号用户数（副号）"
          type: "INTEGER"
          required: false
          rules:
            - ruleId: "VGOP1-R2.11-24101.mcnnum.format"
              ruleName: "副号用户数格式校验"
              type: "FORMAT"
              pattern: "^(\\d{1,12}|)$"
              enabled: true
              severity: "WARNING"
              message: "副号用户数应为数字格式（最大12位）"
            - ruleId: "VGOP1-R2.11-24101.mcnnum.range"
              ruleName: "副号用户数范围校验"
              type: "RANGE"
              min: 0
              max: 2147483647
              enabled: true
              severity: "WARNING"
              message: "副号用户数应在合理范围内（0-2147483647）"
              
        # 第5位：客户端活跃数（主号） - NUMBER(12)
        - fieldIndex: 4
          fieldName: "appactivenum"
          description: "客户端活跃数（主号）"
          type: "INTEGER"
          required: false
          rules:
            - ruleId: "VGOP1-R2.11-24101.appactivenum.format"
              ruleName: "客户端活跃数格式校验"
              type: "FORMAT"
              pattern: "^(\\d{1,12}|)$"
              enabled: true
              severity: "WARNING"
              message: "客户端活跃数应为数字格式（最大12位）"
            - ruleId: "VGOP1-R2.11-24101.appactivenum.range"
              ruleName: "客户端活跃数范围校验"
              type: "RANGE"
              min: 0
              max: 2147483647
              enabled: true
              severity: "WARNING"
              message: "客户端活跃数应在合理范围内（0-2147483647）"
              
        # 第6位：副号码活跃用户数（副号） - NUMBER(12)
        - fieldIndex: 5
          fieldName: "mcnactivenum"
          description: "副号码活跃用户数（副号）"
          type: "INTEGER"
          required: false
          rules:
            - ruleId: "VGOP1-R2.11-24101.mcnactivenum.format"
              ruleName: "副号码活跃用户数格式校验"
              type: "FORMAT"
              pattern: "^(\\d{1,12}|)$"
              enabled: true
              severity: "WARNING"
              message: "副号码活跃用户数应为数字格式（最大12位）"
            - ruleId: "VGOP1-R2.11-24101.mcnactivenum.range"
              ruleName: "副号码活跃用户数范围校验"
              type: "RANGE"
              min: 0
              max: 2147483647
              enabled: true
              severity: "WARNING"
              message: "副号码活跃用户数应在合理范围内（0-2147483647）"
              
        # 第7位：付费用户数（副号） - NUMBER(12)
        - fieldIndex: 6
          fieldName: "paynum"
          description: "付费用户数（副号）"
          type: "INTEGER"
          required: false
          rules:
            - ruleId: "VGOP1-R2.11-24101.paynum.format"
              ruleName: "付费用户数格式校验"
              type: "FORMAT"
              pattern: "^(\\d{1,12}|)$"
              enabled: true
              severity: "WARNING"
              message: "付费用户数应为数字格式（最大12位）"
            - ruleId: "VGOP1-R2.11-24101.paynum.range"
              ruleName: "付费用户数范围校验"
              type: "RANGE"
              min: 0
              max: 2147483647
              enabled: true
              severity: "WARNING"
              message: "付费用户数应在合理范围内（0-2147483647）"
              
        # 第8位：免费用户数（副号） - NUMBER(12)
        - fieldIndex: 7
          fieldName: "feenum"
          description: "免费用户数（副号）"
          type: "INTEGER"
          required: false
          rules:
            - ruleId: "VGOP1-R2.11-24101.feenum.format"
              ruleName: "免费用户数格式校验"
              type: "FORMAT"
              pattern: "^(\\d{1,12}|)$"
              enabled: true
              severity: "WARNING"
              message: "免费用户数应为数字格式（最大12位）"
            - ruleId: "VGOP1-R2.11-24101.feenum.range"
              ruleName: "免费用户数范围校验"
              type: "RANGE"
              min: 0
              max: 2147483647
              enabled: true
              severity: "WARNING"
              message: "免费用户数应在合理范围内（0-2147483647）"
              
        # 第9位：第二卡槽主号数 - NUMBER(12)
        - fieldIndex: 8
          fieldName: "secphonenum"
          description: "第二卡槽主号数"
          type: "INTEGER"
          required: false
          rules:
            - ruleId: "VGOP1-R2.11-24101.secphonenum.format"
              ruleName: "第二卡槽主号数格式校验"
              type: "FORMAT"
              pattern: "^(\\d{1,12}|)$"
              enabled: true
              severity: "WARNING"
              message: "第二卡槽主号数应为数字格式（最大12位）"
            - ruleId: "VGOP1-R2.11-24101.secphonenum.range"
              ruleName: "第二卡槽主号数范围校验"
              type: "RANGE"
              min: 0
              max: 2147483647
              enabled: true
              severity: "WARNING"
              message: "第二卡槽主号数应在合理范围内（0-2147483647）"
              
        # 第10位：第二卡槽副号数 - NUMBER(12)
        - fieldIndex: 9
          fieldName: "secmcnnum"
          description: "第二卡槽副号数"
          type: "INTEGER"
          required: false
          rules:
            - ruleId: "VGOP1-R2.11-24101.secmcnnum.format"
              ruleName: "第二卡槽副号数格式校验"
              type: "FORMAT"
              pattern: "^(\\d{1,12}|)$"
              enabled: true
              severity: "WARNING"
              message: "第二卡槽副号数应为数字格式（最大12位）"
            - ruleId: "VGOP1-R2.11-24101.secmcnnum.range"
              ruleName: "第二卡槽副号数范围校验"
              type: "RANGE"
              min: 0
              max: 2147483647
              enabled: true
              severity: "WARNING"
              message: "第二卡槽副号数应在合理范围内（0-2147483647）"
              
        # 第11位：平台用户总数 - NUMBER(12)
        - fieldIndex: 10
          fieldName: "amcnnum"
          description: "平台用户总数"
          type: "INTEGER"
          required: false
          rules:
            - ruleId: "VGOP1-R2.11-24101.amcnnum.format"
              ruleName: "平台用户总数格式校验"
              type: "FORMAT"
              pattern: "^(\\d{1,12}|)$"
              enabled: true
              severity: "WARNING"
              message: "平台用户总数应为数字格式（最大12位）"
            - ruleId: "VGOP1-R2.11-24101.amcnnum.range"
              ruleName: "平台用户总数范围校验"
              type: "RANGE"
              min: 0
              max: 2147483647
              enabled: true
              severity: "WARNING"
              message: "平台用户总数应在合理范围内（0-2147483647）"

      # 业务逻辑校验规则
      businessRules:
        # 校验用户数逻辑关系：活跃数不应超过总数
        - ruleId: "VGOP1-R2.11-24101.business.active_vs_total_logic"
          ruleName: "活跃用户数与总用户数逻辑校验"
          description: "活跃用户数不应超过对应的总用户数"
          condition: "phonenum is not null and phonenum != '' and appactivenum is not null and appactivenum != ''"
          assertion: "cast(appactivenum as integer) <= cast(phonenum as integer)"
          enabled: true
          severity: "WARNING"
          message: "客户端活跃数不应超过主号用户总数"
          
        # 校验副号活跃数与副号总数关系
        - ruleId: "VGOP1-R2.11-24101.business.mcn_active_vs_total_logic"
          ruleName: "副号活跃数与副号总数逻辑校验"
          description: "副号活跃数不应超过副号总数"
          condition: "mcnnum is not null and mcnnum != '' and mcnactivenum is not null and mcnactivenum != ''"
          assertion: "cast(mcnactivenum as integer) <= cast(mcnnum as integer)"
          enabled: true
          severity: "WARNING"
          message: "副号码活跃用户数不应超过副号用户总数"
          
        # 校验付费用户数与免费用户数关系
        - ruleId: "VGOP1-R2.11-24101.business.pay_vs_free_logic"
          ruleName: "付费用户数与免费用户数逻辑校验"
          description: "付费用户数和免费用户数之和不应超过副号总数"
          condition: "mcnnum is not null and mcnnum != '' and paynum is not null and paynum != '' and feenum is not null and feenum != ''"
          assertion: "cast(paynum as integer) + cast(feenum as integer) <= cast(mcnnum as integer)"
          enabled: true
          severity: "WARNING"
          message: "付费用户数和免费用户数之和不应超过副号用户总数"
          
        # 校验平台用户总数计算逻辑
        - ruleId: "VGOP1-R2.11-24101.business.platform_total_logic"
          ruleName: "平台用户总数计算逻辑校验"
          description: "平台用户总数应包括和多号业务的副号数和第二卡槽副号数"
          condition: "amcnnum is not null and amcnnum != '' and mcnnum is not null and mcnnum != '' and secmcnnum is not null and secmcnnum != ''"
          assertion: "cast(amcnnum as integer) >= cast(mcnnum as integer) + cast(secmcnnum as integer)"
          enabled: true
          severity: "WARNING"
          message: "平台用户总数应大于等于副号数和第二卡槽副号数之和"
          
        # 校验省份地市编码一致性
        - ruleId: "VGOP1-R2.11-24101.business.province_location_consistency"
          ruleName: "省份地市编码一致性校验"
          description: "省份编码与地市编码应保持地理位置一致性"
          condition: "provinceid is not null and provinceid != '' and locationid is not null and locationid != ''"
          assertion: "provinceid != '000' implies locationid != '00000'"
          enabled: true
          severity: "INFO"
          message: "有明确省份编码时，地市编码通常也应该明确"
          
        # 校验统计数据合理性
        - ruleId: "VGOP1-R2.11-24101.business.statistics_reasonableness"
          ruleName: "统计数据合理性校验"
          description: "各项统计数据应在合理范围内"
          condition: "true"
          assertion: "true"
          enabled: true
          severity: "INFO"
          message: "数据来源：存储过程bmssp_VGOP_banalyse生成的业务分析统计结果"

    # 24301接口配置 - 服务类型维表
    'VGOP1-R2-13-24301':
      delimiter: "\\|"
      headerLine: false
      fields:
        # 第1位：业务代码 - VARCHAR2(10)
        - fieldIndex: 0
          fieldName: "ServID"
          description: "业务代码"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.13-24301.servid.format"
              ruleName: "业务代码格式校验"
              type: "FORMAT"
              pattern: "^(\\d{6}|)$"
              enabled: true
              severity: "WARNING"
              message: "业务代码应为6位数字型字符串"
            - ruleId: "VGOP1-R2.13-24301.servid.length"
              ruleName: "业务代码长度校验"
              type: "LENGTH"
              max: 10
              enabled: true
              severity: "WARNING"
              message: "业务代码长度不能超过10位"
            - ruleId: "VGOP1-R2.13-24301.servid.standard_format"
              ruleName: "业务代码标准格式校验"
              type: "LENGTH"
              exact: 6
              enabled: true
              severity: "INFO"
              message: "业务代码标准长度为6位数字"
              
        # 第2位：业务名称 - VARCHAR2(50)
        - fieldIndex: 1
          fieldName: "ServName"
          description: "业务名称"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.13-24301.servname.length"
              ruleName: "业务名称长度校验"
              type: "LENGTH"
              max: 50
              enabled: true
              severity: "WARNING"
              message: "业务名称长度不能超过50位"
            - ruleId: "VGOP1-R2.13-24301.servname.content"
              ruleName: "业务名称内容校验"
              type: "FORMAT"
              pattern: "^[\\u4e00-\\u9fa5a-zA-Z0-9\\s\\-_()（）]*$"
              enabled: true
              severity: "WARNING"
              message: "业务名称应包含有效字符（中英文、数字、常用符号）"

      # 业务逻辑校验规则
      businessRules:
        # 校验业务代码与业务名称的对应关系
        - ruleId: "VGOP1-R2.13-24301.business.servid_servname_mapping"
          ruleName: "业务代码与业务名称对应关系校验"
          description: "业务代码和业务名称应该成对出现"
          condition: "ServID is not null and ServID != ''"
          assertion: "ServName is not null and ServName != ''"
          enabled: true
          severity: "ERROR"
          message: "有业务代码时，业务名称不能为空"
          
        # 校验业务代码唯一性
        - ruleId: "VGOP1-R2.13-24301.business.servid_uniqueness"
          ruleName: "业务代码唯一性校验"
          description: "业务代码在维表中应该唯一"
          condition: "ServID is not null and ServID != ''"
          assertion: "true"
          enabled: true
          severity: "WARNING"
          message: "业务代码应在维表中保持唯一性"
          
        # 校验业务名称合理性
        - ruleId: "VGOP1-R2.13-24301.business.servname_reasonableness"
          ruleName: "业务名称合理性校验"
          description: "业务名称应该具有业务含义"
          condition: "ServName is not null and ServName != ''"
          assertion: "length(ServName) >= 2"
          enabled: true
          severity: "WARNING"
          message: "业务名称长度应至少2位字符，确保有实际业务含义"
          
        # 校验维表数据完整性
        - ruleId: "VGOP1-R2.13-24301.business.dimension_data_integrity"
          ruleName: "维表数据完整性校验"
          description: "维表数据应该完整，字段不应全为空"
          condition: "true"
          assertion: "ServID is not null and ServID != '' or ServName is not null and ServName != ''"
          enabled: true
          severity: "ERROR"
          message: "维表记录不应该业务代码和业务名称都为空"
          
        # 校验维表数据稳定性
        - ruleId: "VGOP1-R2.13-24301.business.dimension_data_stability"
          ruleName: "维表数据稳定性校验"
          description: "维表数据应该相对稳定，不会频繁变动"
          condition: "true"
          assertion: "true"
          enabled: true
          severity: "INFO"
          message: "维表数据：服务类型字典表，数据应相对稳定"

    # 24302接口配置 - 渠道信息维表
    'VGOP1-R2-13-24302':
      delimiter: "\\|"
      headerLine: false
      fields:
        # 第1位：开户媒介编码 - VARCHAR2(2)
        - fieldIndex: 0
          fieldName: "channelcode"
          description: "开户媒介编码"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.13-24302.channelcode.format"
              ruleName: "开户媒介编码格式校验"
              type: "FORMAT"
              pattern: "^(\\d{1,2}|)$"
              enabled: true
              severity: "WARNING"
              message: "开户媒介编码应为数字型字符串"
            - ruleId: "VGOP1-R2.13-24302.channelcode.length"
              ruleName: "开户媒介编码长度校验"
              type: "LENGTH"
              max: 2
              enabled: true
              severity: "WARNING"
              message: "开户媒介编码长度不能超过2位"
            - ruleId: "VGOP1-R2.13-24302.channelcode.range"
              ruleName: "开户媒介编码范围校验"
              type: "RANGE"
              min: 1
              max: 99
              enabled: true
              severity: "INFO"
              message: "开户媒介编码应在合理范围内（1-99）"
              
        # 第2位：开户媒介名称 - VARCHAR2(50)
        - fieldIndex: 1
          fieldName: "channelname"
          description: "开户媒介名称"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.13-24302.channelname.length"
              ruleName: "开户媒介名称长度校验"
              type: "LENGTH"
              max: 50
              enabled: true
              severity: "WARNING"
              message: "开户媒介名称长度不能超过50位"
            - ruleId: "VGOP1-R2.13-24302.channelname.content"
              ruleName: "开户媒介名称内容校验"
              type: "FORMAT"
              pattern: "^[\\u4e00-\\u9fa5a-zA-Z0-9\\s\\-_()（）]*$"
              enabled: true
              severity: "WARNING"
              message: "开户媒介名称应包含有效字符（中英文、数字、常用符号）"

      # 业务逻辑校验规则
      businessRules:
        # 校验开户媒介编码与名称的对应关系
        - ruleId: "VGOP1-R2.13-24302.business.channelcode_channelname_mapping"
          ruleName: "开户媒介编码与名称对应关系校验"
          description: "开户媒介编码和名称应该成对出现"
          condition: "channelcode is not null and channelcode != ''"
          assertion: "channelname is not null and channelname != ''"
          enabled: true
          severity: "ERROR"
          message: "有开户媒介编码时，开户媒介名称不能为空"
          
        # 校验开户媒介编码唯一性
        - ruleId: "VGOP1-R2.13-24302.business.channelcode_uniqueness"
          ruleName: "开户媒介编码唯一性校验"
          description: "开户媒介编码在维表中应该唯一"
          condition: "channelcode is not null and channelcode != ''"
          assertion: "true"
          enabled: true
          severity: "WARNING"
          message: "开户媒介编码应在维表中保持唯一性"
          
        # 校验开户媒介名称合理性
        - ruleId: "VGOP1-R2.13-24302.business.channelname_reasonableness"
          ruleName: "开户媒介名称合理性校验"
          description: "开户媒介名称应该具有业务含义"
          condition: "channelname is not null and channelname != ''"
          assertion: "length(channelname) >= 2"
          enabled: true
          severity: "WARNING"
          message: "开户媒介名称长度应至少2位字符，确保有实际业务含义"
          
        # 校验维表数据完整性
        - ruleId: "VGOP1-R2.13-24302.business.dimension_data_integrity"
          ruleName: "维表数据完整性校验"
          description: "维表数据应该完整，字段不应全为空"
          condition: "true"
          assertion: "channelcode is not null and channelcode != '' or channelname is not null and channelname != ''"
          enabled: true
          severity: "ERROR"
          message: "维表记录不应该开户媒介编码和名称都为空"
          
        # 校验渠道编码业务逻辑
        - ruleId: "VGOP1-R2.13-24302.business.channel_business_logic"
          ruleName: "渠道编码业务逻辑校验"
          description: "渠道编码应符合业务规则（如：1和多号平台自有渠道、2省公司渠道、3第三方渠道等）"
          condition: "channelcode is not null and channelcode != ''"
          assertion: "channelcode matches '^[1-9]\\d*$'"
          enabled: true
          severity: "INFO"
          message: "渠道编码应为正整数，符合业务编码规范"
          
        # 校验维表数据稳定性
        - ruleId: "VGOP1-R2.13-24302.business.dimension_data_stability"
          ruleName: "维表数据稳定性校验"
          description: "维表数据应该相对稳定，不会频繁变动"
          condition: "true"
          assertion: "true"
          enabled: true
          severity: "INFO"
          message: "维表数据：渠道信息字典表，数据应相对稳定"

    # 24303接口配置 - 停机标识维表
    'VGOP1-R2-13-24303':
      delimiter: "\\|"
      headerLine: false
      fields:
        # 第1位：停机标识 - VARCHAR2(2)
        - fieldIndex: 0
          fieldName: "shutdown"
          description: "停机标识"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.13-24303.shutdown.format"
              ruleName: "停机标识格式校验"
              type: "FORMAT"
              pattern: "^(\\d{1,2}|)$"
              enabled: true
              severity: "WARNING"
              message: "停机标识应为数字型字符串"
            - ruleId: "VGOP1-R2.13-24303.shutdown.length"
              ruleName: "停机标识长度校验"
              type: "LENGTH"
              max: 2
              enabled: true
              severity: "WARNING"
              message: "停机标识长度不能超过2位"
            - ruleId: "VGOP1-R2.13-24303.shutdown.range"
              ruleName: "停机标识范围校验"
              type: "RANGE"
              min: 0
              max: 99
              enabled: true
              severity: "INFO"
              message: "停机标识应在合理范围内（00-99）"
              
        # 第2位：停机标识字段中文说明 - VARCHAR2(50)
        - fieldIndex: 1
          fieldName: "shutdownname"
          description: "停机标识字段中文说明"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.13-24303.shutdownname.length"
              ruleName: "停机标识说明长度校验"
              type: "LENGTH"
              max: 50
              enabled: true
              severity: "WARNING"
              message: "停机标识说明长度不能超过50位"
            - ruleId: "VGOP1-R2.13-24303.shutdownname.content"
              ruleName: "停机标识说明内容校验"
              type: "FORMAT"
              pattern: "^[\\u4e00-\\u9fa5a-zA-Z0-9\\s\\-_()（）]*$"
              enabled: true
              severity: "WARNING"
              message: "停机标识说明应包含有效字符（中英文、数字、常用符号）"

      # 业务逻辑校验规则
      businessRules:
        # 校验停机标识与说明的对应关系
        - ruleId: "VGOP1-R2.13-24303.business.shutdown_shutdownname_mapping"
          ruleName: "停机标识与说明对应关系校验"
          description: "停机标识和说明应该成对出现"
          condition: "shutdown is not null and shutdown != ''"
          assertion: "shutdownname is not null and shutdownname != ''"
          enabled: true
          severity: "ERROR"
          message: "有停机标识时，停机标识说明不能为空"
          
        # 校验停机标识唯一性
        - ruleId: "VGOP1-R2.13-24303.business.shutdown_uniqueness"
          ruleName: "停机标识唯一性校验"
          description: "停机标识在维表中应该唯一"
          condition: "shutdown is not null and shutdown != ''"
          assertion: "true"
          enabled: true
          severity: "WARNING"
          message: "停机标识应在维表中保持唯一性"
          
        # 校验停机标识说明合理性
        - ruleId: "VGOP1-R2.13-24303.business.shutdownname_reasonableness"
          ruleName: "停机标识说明合理性校验"
          description: "停机标识说明应该具有业务含义"
          condition: "shutdownname is not null and shutdownname != ''"
          assertion: "length(shutdownname) >= 2"
          enabled: true
          severity: "WARNING"
          message: "停机标识说明长度应至少2位字符，确保有实际业务含义"
          
        # 校验维表数据完整性
        - ruleId: "VGOP1-R2.13-24303.business.dimension_data_integrity"
          ruleName: "维表数据完整性校验"
          description: "维表数据应该完整，字段不应全为空"
          condition: "true"
          assertion: "shutdown is not null and shutdown != '' or shutdownname is not null and shutdownname != ''"
          enabled: true
          severity: "ERROR"
          message: "维表记录不应该停机标识和说明都为空"
          
        # 校验停机标识业务逻辑
        - ruleId: "VGOP1-R2.13-24303.business.shutdown_business_logic"
          ruleName: "停机标识业务逻辑校验"
          description: "停机标识应符合业务规则（如：正常状态、各种停机状态等）"
          condition: "shutdown is not null and shutdown != ''"
          assertion: "shutdown matches '^\\d{1,2}$'"
          enabled: true
          severity: "INFO"
          message: "停机标识应为数字编码，符合业务编码规范"
          
        # 校验维表数据稳定性
        - ruleId: "VGOP1-R2.13-24303.business.dimension_data_stability"
          ruleName: "维表数据稳定性校验"
          description: "维表数据应该相对稳定，不会频繁变动"
          condition: "true"
          assertion: "true"
          enabled: true
          severity: "INFO"
          message: "维表数据：停机标识字典表，数据应相对稳定"

    # 24201月接口配置 - 主号用户信息月度查询（基于VGOP1-R2.10-24201month.sh脚本）
    'VGOP1-R2.10-24201-MONTHLY':
      delimiter: "\\|"
      headerLine: false
      fields:
        # 第1位：用户主号号码 - 手机号 VARCHAR2(14)
        - fieldIndex: 0
          fieldName: "phonenumber"
          description: "用户主号号码"
          type: "STRING"
          required: true
          rules:
            - ruleId: "VGOP1-R2.10-24201-MONTHLY.phonenumber.required"
              ruleName: "手机号码不能为空"
              type: "REQUIRED"
              enabled: true
              severity: "ERROR"
              message: "手机号码是必填项"
            - ruleId: "VGOP1-R2.10-24201-MONTHLY.phonenumber.format"
              ruleName: "手机号格式校验"
              type: "FORMAT"
              pattern: "^(1[3-9]\\d{9}|00\\d{11,13})$"
              enabled: true
              severity: "ERROR"
              message: "手机号格式不正确，应为不带+86的11位数字号码或兼容国际号码加字冠00"
            - ruleId: "VGOP1-R2.10-24201-MONTHLY.phonenumber.length"
              ruleName: "手机号码长度校验"
              type: "LENGTH"
              max: 14
              enabled: true
              severity: "WARNING"
              message: "手机号码长度不能超过14位"

        # 第2位：主号码业务状态 - VARCHAR2(1)
        - fieldIndex: 1
          fieldName: "phonestate"
          description: "主号码业务状态"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24201-MONTHLY.phonestate.enum"
              ruleName: "业务状态枚举值校验"
              type: "ENUM"
              values: ["0", "1", ""]
              enabled: true
              severity: "WARNING"
              message: "业务状态应为：0(正常)、1(暂停)或空值"
            - ruleId: "VGOP1-R2.10-24201-MONTHLY.phonestate.length"
              ruleName: "业务状态长度校验"
              type: "LENGTH"
              exact: 1
              enabled: true
              severity: "WARNING"
              message: "业务状态长度应为1位"

        # 第3位：主号码的IMSI号码 - VARCHAR2(16)
        - fieldIndex: 2
          fieldName: "phoneimsi"
          description: "主号码的IMSI号码"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24201-MONTHLY.phoneimsi.length"
              ruleName: "IMSI长度校验"
              type: "LENGTH"
              max: 16
              enabled: true
              severity: "WARNING"
              message: "IMSI号码长度不能超过16位"
            - ruleId: "VGOP1-R2.10-24201-MONTHLY.phoneimsi.format"
              ruleName: "IMSI格式校验"
              type: "FORMAT"
              pattern: "^(\\d{15,16}|)$"
              enabled: true
              severity: "WARNING"
              message: "IMSI应为15-16位数字"

        # 第4位：主号码的IMEI号 - VARCHAR2(15)
        - fieldIndex: 3
          fieldName: "phoneimei"
          description: "主号码的IMEI号"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24201-MONTHLY.phoneimei.length"
              ruleName: "IMEI长度校验"
              type: "LENGTH"
              max: 15
              enabled: true
              severity: "WARNING"
              message: "IMEI号码长度不能超过15位"
            - ruleId: "VGOP1-R2.10-24201-MONTHLY.phoneimei.format"
              ruleName: "IMEI格式校验"
              type: "FORMAT"
              pattern: "^(\\d{15}|)$"
              enabled: true
              severity: "WARNING"
              message: "IMEI应为15位数字"

        # 第5位：主号码归属区 - VARCHAR2(5)
        - fieldIndex: 4
          fieldName: "locationid"
          description: "主号码归属区"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24201-MONTHLY.locationid.length"
              ruleName: "归属区长度校验"
              type: "LENGTH"
              max: 5
              enabled: true
              severity: "WARNING"
              message: "归属区长度不能超过5位，不带0"
            - ruleId: "VGOP1-R2.10-24201-MONTHLY.locationid.format"
              ruleName: "归属区格式校验"
              type: "FORMAT"
              pattern: "^([1-9]\\d{0,4}|)$"
              enabled: true
              severity: "WARNING"
              message: "归属区不带0，应为数字"

        # 第6位：主号码省ID - VARCHAR2(5)
        - fieldIndex: 5
          fieldName: "provinceid"
          description: "主号码省ID"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24201-MONTHLY.provinceid.length"
              ruleName: "省ID长度校验"
              type: "LENGTH"
              max: 5
              enabled: true
              severity: "WARNING"
              message: "省ID长度不能超过5位"
            - ruleId: "VGOP1-R2.10-24201-MONTHLY.provinceid.format"
              ruleName: "省ID格式校验"
              type: "FORMAT"
              pattern: "^(\\d{1,5}|)$"
              enabled: true
              severity: "WARNING"
              message: "省ID应为数字"

        # 第7位：开户时间 - VARCHAR2(14)
        - fieldIndex: 6
          fieldName: "openingtime"
          description: "开户时间"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24201-MONTHLY.openingtime.format"
              ruleName: "开户时间格式校验"
              type: "FORMAT"
              pattern: "^(20\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])[0-5]\\d[0-5]\\d|)$"
              enabled: true
              severity: "WARNING"
              message: "开户时间格式应为YYYYMMDDHH24MISS（14位数字）或空值"
            - ruleId: "VGOP1-R2.10-24201-MONTHLY.openingtime.length"
              ruleName: "开户时间长度校验"
              type: "LENGTH"
              exact: 14
              enabled: true
              severity: "WARNING"
              message: "开户时间长度应为14位"

        # 第8位：操作时间 - VARCHAR2(14)
        - fieldIndex: 7
          fieldName: "Optime"
          description: "操作时间"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24201-MONTHLY.optime.format"
              ruleName: "操作时间格式校验"
              type: "FORMAT"
              pattern: "^(20\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])[0-5]\\d[0-5]\\d|)$"
              enabled: true
              severity: "WARNING"
              message: "操作时间格式应为YYYYMMDDHH24MISS（14位数字）或空值"
            - ruleId: "VGOP1-R2.10-24201-MONTHLY.optime.length"
              ruleName: "操作时间长度校验"
              type: "LENGTH"
              exact: 14
              enabled: true
              severity: "WARNING"
              message: "操作时间长度应为14位"

        # 第9位：网站用户头像性别 - VARCHAR2(1)
        - fieldIndex: 8
          fieldName: "sex"
          description: "网站用户头像性别"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24201-MONTHLY.sex.enum"
              ruleName: "性别枚举值校验"
              type: "ENUM"
              values: ["0", "1", ""]
              enabled: true
              severity: "WARNING"
              message: "性别应为：0(男)、1(女)或空值"
            - ruleId: "VGOP1-R2.10-24201-MONTHLY.sex.length"
              ruleName: "性别长度校验"
              type: "LENGTH"
              exact: 1
              enabled: true
              severity: "WARNING"
              message: "性别长度应为1位"

      # 业务逻辑校验规则（基于脚本WHERE条件：mum.openingtime<'{endtime}'）
      businessRules:
        # 校验WHERE条件约束 - 开户时间截止约束
        - ruleId: "VGOP1-R2.10-24201-MONTHLY.business.opening_time_constraint"
          ruleName: "开户时间截止约束校验"
          description: "基于脚本WHERE条件的数据约束：开户时间应小于统计截止时间"
          condition: "openingtime is not null and openingtime != ''"
          assertion: "length(openingtime) = 14"
          enabled: true
          severity: "ERROR"
          message: "开户时间必须为14位时间格式，符合脚本WHERE条件约束"

        # 校验手机号码与IMSI的关联性
        - ruleId: "VGOP1-R2.10-24201-MONTHLY.business.phone_imsi_relationship"
          ruleName: "手机号码与IMSI关联性校验"
          description: "正常状态的手机号码通常应该有对应的IMSI"
          condition: "phonestate = '0' and phonenumber is not null and phonenumber != ''"
          assertion: "phoneimsi is not null and phoneimsi != ''"
          enabled: true
          severity: "WARNING"
          message: "正常状态的手机号码通常应该有对应的IMSI号码"

        # 校验省份与归属地的一致性
        - ruleId: "VGOP1-R2.10-24201-MONTHLY.business.province_location_consistency"
          ruleName: "省份与归属地一致性校验"
          description: "省ID与归属区应保持地理位置一致性"
          condition: "provinceid is not null and provinceid != '' and locationid is not null and locationid != ''"
          assertion: "provinceid != '000' implies locationid != '00000'"
          enabled: true
          severity: "INFO"
          message: "有明确省份ID时，归属区编码通常也应该明确"

        # 校验开户时间与操作时间的逻辑性
        - ruleId: "VGOP1-R2.10-24201-MONTHLY.business.time_logic_check"
          ruleName: "开户时间与操作时间逻辑性校验"
          description: "操作时间应该大于等于开户时间"
          condition: "openingtime is not null and openingtime != '' and Optime is not null and Optime != ''"
          assertion: "Optime >= openingtime"
          enabled: true
          severity: "WARNING"
          message: "操作时间应该大于等于开户时间"

        # 校验月度统计数据范围
        - ruleId: "VGOP1-R2.10-24201-MONTHLY.business.monthly_data_range"
          ruleName: "月度统计数据范围校验"
          description: "月度查询应包含截止到月初的历史数据"
          condition: "true"
          assertion: "true"
          enabled: true
          severity: "INFO"
          message: "数据来源：月度查询，基于脚本where mum.openingtime<'{endtime}'的历史数据统计"

        # 校验字段转换逻辑 - 省ID提取逻辑
        - ruleId: "VGOP1-R2.10-24201-MONTHLY.business.province_extraction_logic"
          ruleName: "省ID提取逻辑校验"
          description: "基于脚本字段转换逻辑：substr(bp.bossid, 1, 3) as provinceid"
          condition: "provinceid is not null and provinceid != ''"
          assertion: "length(provinceid) <= 3"
          enabled: true
          severity: "WARNING"
          message: "省ID应为从bossid提取的前3位，长度不超过3位"

    # 24202月接口配置 - 副号码明细月度查询（基于VGOP1-R2.10-24202month.sh脚本）
    'VGOP1-R2.10-24202-MONTHLY':
      delimiter: "\\|"
      headerLine: false
      fields:
        # 第1位：用户副号号码 - 手机号 VARCHAR2(11)
        - fieldIndex: 0
          fieldName: "mcnnumber"
          description: "用户副号号码"
          type: "MOBILE"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.mcnnumber.format"
              ruleName: "副号码格式校验"
              type: "FORMAT"
              pattern: "^(1[3-9]\\d{9}|)$"
              enabled: true
              severity: "ERROR"
              message: "副号码格式不正确，应为不带+86的11位数字号码"
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.mcnnumber.length"
              ruleName: "副号码长度校验"
              type: "LENGTH"
              exact: 11
              enabled: true
              severity: "ERROR"
              message: "副号码长度必须为11位"

        # 第2位：用户主号号码 - 手机号 VARCHAR2(14)
        - fieldIndex: 1
          fieldName: "phonenumber"
          description: "用户主号号码"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.phonenumber.format"
              ruleName: "主号码格式校验"
              type: "FORMAT"
              pattern: "^(1[3-9]\\d{9}|00\\d{11,13}|)$"
              enabled: true
              severity: "ERROR"
              message: "主号码格式不正确，应为不带+86的11位数字号码或兼容国际号码加字冠00"
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.phonenumber.length"
              ruleName: "主号码长度校验"
              type: "LENGTH"
              max: 14
              enabled: true
              severity: "WARNING"
              message: "主号码长度不能超过14位"

        # 第3位：副号码业务状态 - VARCHAR2(1)
        - fieldIndex: 2
          fieldName: "business"
          description: "副号码业务状态"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.business.enum"
              ruleName: "业务状态枚举值校验"
              type: "ENUM"
              values: ["0", "1", "2", "3", "4", ""]
              enabled: true
              severity: "WARNING"
              message: "业务状态应为：0(正常)、1(预开户)、2(申请中)、3(取消中)、4(预验证)或空值"
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.business.length"
              ruleName: "业务状态长度校验"
              type: "LENGTH"
              exact: 1
              enabled: true
              severity: "WARNING"
              message: "业务状态长度应为1位"

        # 第4位：副号码停机标识 - VARCHAR2(2)
        - fieldIndex: 3
          fieldName: "shutdown"
          description: "副号码停机标识"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.shutdown.length"
              ruleName: "停机标识长度校验"
              type: "LENGTH"
              max: 2
              enabled: true
              severity: "WARNING"
              message: "停机标识长度不能超过2位"
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.shutdown.reference"
              ruleName: "停机标识维表引用校验"
              type: "FORMAT"
              pattern: "^(\\d{1,2}|)$"
              enabled: true
              severity: "INFO"
              message: "停机标识应对应24303-副号码停机标识维表中的停机标识"

        # 第5位：副号码的IMSI号 - VARCHAR2(16)
        - fieldIndex: 4
          fieldName: "mcnimsi"
          description: "副号码的IMSI号"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.mcnimsi.length"
              ruleName: "IMSI长度校验"
              type: "LENGTH"
              max: 16
              enabled: true
              severity: "WARNING"
              message: "IMSI号码长度不能超过16位"
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.mcnimsi.format"
              ruleName: "IMSI格式校验"
              type: "FORMAT"
              pattern: "^(\\d{15,16}|)$"
              enabled: true
              severity: "WARNING"
              message: "IMSI应为15-16位数字"

        # 第6位：副号码归属区号 - VARCHAR2(5)
        - fieldIndex: 5
          fieldName: "mcnlocationid"
          description: "副号码归属区号"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.mcnlocationid.length"
              ruleName: "归属区号长度校验"
              type: "LENGTH"
              max: 5
              enabled: true
              severity: "WARNING"
              message: "归属区号长度不能超过5位，不带0"
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.mcnlocationid.format"
              ruleName: "归属区号格式校验"
              type: "FORMAT"
              pattern: "^([1-9]\\d{0,4}|)$"
              enabled: true
              severity: "WARNING"
              message: "归属区号不带0，应为数字"

        # 第7位：副号功能状态 - VARCHAR2(7)
        - fieldIndex: 6
          fieldName: "numstate"
          description: "副号功能状态"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.numstate.length"
              ruleName: "功能状态长度校验"
              type: "LENGTH"
              exact: 7
              enabled: true
              severity: "WARNING"
              message: "功能状态长度应为7位，形如'1000000'"
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.numstate.format"
              ruleName: "功能状态格式校验"
              type: "FORMAT"
              pattern: "^([01]{7}|)$"
              enabled: true
              severity: "WARNING"
              message: "功能状态应为7位二进制数字，各位分别表示逻辑开关机状态等功能开关"

        # 第8位：副号码类型 - VARCHAR2(1)
        - fieldIndex: 7
          fieldName: "mcnnature"
          description: "副号码类型"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.mcnnature.enum"
              ruleName: "副号码类型枚举值校验"
              type: "ENUM"
              values: ["0", "1", ""]
              enabled: true
              severity: "WARNING"
              message: "副号码类型应为：0(虚拟副号码)、1(实体副号码)或空值"
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.mcnnature.length"
              ruleName: "副号类型长度校验"
              type: "LENGTH"
              exact: 1
              enabled: true
              severity: "WARNING"
              message: "副号码类型长度应为1位"

        # 第9位：副号码序号 - VARCHAR2(2)
        - fieldIndex: 8
          fieldName: "mcnnum"
          description: "副号码序号"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.mcnnum.enum"
              ruleName: "副号序号枚举值校验"
              type: "ENUM"
              values: ["1", "2", "3", ""]
              enabled: true
              severity: "ERROR"
              message: "副号序号应为：1(第一个副号)、2(第二个副号)、3(第三个副号)或空值"
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.mcnnum.length"
              ruleName: "副号序号长度校验"
              type: "LENGTH"
              max: 2
              enabled: true
              severity: "WARNING"
              message: "副号序号长度不能超过2位"

        # 第10位：副号码开户媒介 - VARCHAR2(2)
        - fieldIndex: 9
          fieldName: "channel"
          description: "副号码开户媒介"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.channel.length"
              ruleName: "开户媒介长度校验"
              type: "LENGTH"
              max: 2
              enabled: true
              severity: "WARNING"
              message: "开户媒介长度不能超过2位"
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.channel.reference"
              ruleName: "开户媒介维表引用校验"
              type: "FORMAT"
              pattern: "^(\\d{1,2}|)$"
              enabled: true
              severity: "INFO"
              message: "开户媒介应对应24302-用户开户媒介维表中的开户媒介编码"

        # 第11位：副号码开户渠道 - VARCHAR2(2)
        - fieldIndex: 10
          fieldName: "mj"
          description: "副号码开户渠道"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.mj.enum"
              ruleName: "开户渠道枚举值校验"
              type: "ENUM"
              values: ["1", "2", "3", ""]
              enabled: true
              severity: "WARNING"
              message: "开户渠道应为：1(和多号平台自有渠道)、2(省公司渠道)、3(第三方渠道)或空值"
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.mj.length"
              ruleName: "开户渠道长度校验"
              type: "LENGTH"
              max: 2
              enabled: true
              severity: "WARNING"
              message: "开户渠道长度不能超过2位"

        # 第12位：开户时间 - VARCHAR2(14)
        - fieldIndex: 11
          fieldName: "openingtime"
          description: "开户时间"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.openingtime.format"
              ruleName: "开户时间格式校验"
              type: "FORMAT"
              pattern: "^(20\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])[0-5]\\d[0-5]\\d|)$"
              enabled: true
              severity: "WARNING"
              message: "开户时间格式应为YYYYMMDDHH24MISS（14位数字）或空值"
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.openingtime.length"
              ruleName: "开户时间长度校验"
              type: "LENGTH"
              exact: 14
              enabled: true
              severity: "WARNING"
              message: "开户时间长度应为14位"

        # 第13位：操作时间 - VARCHAR2(14)
        - fieldIndex: 12
          fieldName: "Optime"
          description: "操作时间"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.optime.format"
              ruleName: "操作时间格式校验"
              type: "FORMAT"
              pattern: "^(20\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])[0-5]\\d[0-5]\\d|)$"
              enabled: true
              severity: "WARNING"
              message: "操作时间格式应为YYYYMMDDHH24MISS（14位数字）或空值"
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.optime.length"
              ruleName: "操作时间长度校验"
              type: "LENGTH"
              exact: 14
              enabled: true
              severity: "WARNING"
              message: "操作时间长度应为14位"

        # 第14位：实体副号码IMSI号码获取时间 - VARCHAR2(14)
        - fieldIndex: 13
          fieldName: "mcimsitime"
          description: "实体副号码IMSI号码获取时间"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.mcimsitime.format"
              ruleName: "IMSI获取时间格式校验"
              type: "FORMAT"
              pattern: "^(20\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])[0-5]\\d[0-5]\\d|)$"
              enabled: true
              severity: "WARNING"
              message: "IMSI获取时间格式应为YYYYMMDDHH24MISS（14位数字）或空值"
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.mcimsitime.length"
              ruleName: "IMSI获取时间长度校验"
              type: "LENGTH"
              exact: 14
              enabled: true
              severity: "WARNING"
              message: "IMSI获取时间长度应为14位"

        # 第15位：用户类别 - VARCHAR2(2)
        - fieldIndex: 14
          fieldName: "usertype"
          description: "用户类别"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.usertype.length"
              ruleName: "用户类别长度校验"
              type: "LENGTH"
              max: 2
              enabled: true
              severity: "WARNING"
              message: "用户类别长度不能超过2位"
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.usertype.reference"
              ruleName: "用户类别维表引用校验"
              type: "FORMAT"
              pattern: "^(\\d{1,2}|)$"
              enabled: true
              severity: "INFO"
              message: "用户类别应对应24304-用户类别维表中的用户类型代码"

        # 第16位：套餐生效时间 - VARCHAR2(14)
        - fieldIndex: 15
          fieldName: "Begintime"
          description: "套餐生效时间"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.begintime.format"
              ruleName: "生效时间格式校验"
              type: "FORMAT"
              pattern: "^(20\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])[0-5]\\d[0-5]\\d|)$"
              enabled: true
              severity: "WARNING"
              message: "生效时间格式应为YYYYMMDDHH24MISS（14位数字）或空值"
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.begintime.length"
              ruleName: "生效时间长度校验"
              type: "LENGTH"
              exact: 14
              enabled: true
              severity: "WARNING"
              message: "生效时间长度应为14位"

        # 第17位：套餐失效时间 - VARCHAR2(14)
        - fieldIndex: 16
          fieldName: "Endtime"
          description: "套餐失效时间"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.endtime.format"
              ruleName: "失效时间格式校验"
              type: "FORMAT"
              pattern: "^(20\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])[0-5]\\d[0-5]\\d|)$"
              enabled: true
              severity: "WARNING"
              message: "失效时间格式应为YYYYMMDDHH24MISS（14位数字）或空值"
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.endtime.length"
              ruleName: "失效时间长度校验"
              type: "LENGTH"
              exact: 14
              enabled: true
              severity: "WARNING"
              message: "失效时间长度应为14位"

        # 第18位：业务代码 - VARCHAR2(10)
        - fieldIndex: 17
          fieldName: "ServID"
          description: "业务代码"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.servid.length"
              ruleName: "业务代码长度校验"
              type: "LENGTH"
              max: 10
              enabled: true
              severity: "WARNING"
              message: "业务代码长度不能超过10位"
            - ruleId: "VGOP1-R2.10-24202-MONTHLY.servid.reference"
              ruleName: "业务代码维表引用校验"
              type: "FORMAT"
              pattern: "^(\\d{1,10}|)$"
              enabled: true
              severity: "INFO"
              message: "业务代码应对应24301-业务类型维表中的业务代码"

      # 业务逻辑校验规则（基于脚本逻辑）
      businessRules:
        # 校验WHERE条件约束 - 基于脚本WHERE条件
        - ruleId: "VGOP1-R2.10-24202-MONTHLY.business.where_constraint"
          ruleName: "WHERE条件约束校验"
          description: "基于脚本WHERE条件：openingtime<{endtime} and phonenumber!='' and phonenumber is not null and mcnnum in (1,2,3)"
          condition: "phonenumber is not null and phonenumber != ''"
          assertion: "mcnnum in ('1', '2', '3')"
          enabled: true
          severity: "ERROR"
          message: "数据不符合脚本WHERE条件：手机号不为空且副号序号必须在1-3之间"

        # 校验字段转换逻辑 - 开户渠道转换逻辑
        - ruleId: "VGOP1-R2.10-24202-MONTHLY.business.channel_transformation"
          ruleName: "开户渠道转换逻辑校验"
          description: "基于脚本字段转换逻辑：case channel when '9' then '3' else '1' end as mj"
          condition: "channel is not null and channel != ''"
          assertion: "(channel = '9' and mj = '3') or (channel != '9' and mj = '1')"
          enabled: true
          severity: "WARNING"
          message: "开户渠道转换逻辑错误：channel为9时mj应为3，其他情况mj应为1"

        # 校验固定值字段
        - ruleId: "VGOP1-R2.10-24202-MONTHLY.business.fixed_value_usertype"
          ruleName: "固定值字段校验"
          description: "基于脚本固定值：'0' as usertype"
          condition: "true"
          assertion: "usertype = '0' or usertype = '' or usertype is null"
          enabled: true
          severity: "WARNING"
          message: "用户类别固定值应为'0'"

        # 校验副号码与主号码的关联性
        - ruleId: "VGOP1-R2.10-24202-MONTHLY.business.mcn_phone_relationship"
          ruleName: "副号码与主号码关联性校验"
          description: "副号码和主号码应该都存在且为有效手机号码"
          condition: "mcnnumber is not null and mcnnumber != '' and phonenumber is not null and phonenumber != ''"
          assertion: "length(mcnnumber) = 11 and length(phonenumber) >= 11"
          enabled: true
          severity: "WARNING"
          message: "副号码和主号码都应该为有效手机号码格式"

        # 校验实体副号码与IMSI的关联性
        - ruleId: "VGOP1-R2.10-24202-MONTHLY.business.entity_mcn_imsi_relationship"
          ruleName: "实体副号码与IMSI关联性校验"
          description: "实体副号码通常应该有对应的IMSI"
          condition: "mcnnature = '1' and mcnnumber is not null and mcnnumber != ''"
          assertion: "mcnimsi is not null and mcnimsi != ''"
          enabled: true
          severity: "WARNING"
          message: "实体副号码通常应该有对应的IMSI号码"

        # 校验副号序号与业务状态的逻辑性
        - ruleId: "VGOP1-R2.10-24202-MONTHLY.business.mcnnum_business_logic"
          ruleName: "副号序号与业务状态逻辑性校验"
          description: "正常状态的副号码序号应该在有效范围内"
          condition: "business = '0' and mcnnum is not null and mcnnum != ''"
          assertion: "mcnnum in ('1', '2', '3')"
          enabled: true
          severity: "ERROR"
          message: "正常状态的副号码序号必须在1-3范围内"

        # 校验时间字段的逻辑一致性
        - ruleId: "VGOP1-R2.10-24202-MONTHLY.business.time_logic_consistency"
          ruleName: "时间字段逻辑一致性校验"
          description: "套餐失效时间应该大于生效时间"
          condition: "Begintime is not null and Begintime != '' and Endtime is not null and Endtime != ''"
          assertion: "Endtime >= Begintime"
          enabled: true
          severity: "WARNING"
          message: "套餐失效时间应该大于等于生效时间"

         # 校验月度统计数据范围
        - ruleId: "VGOP1-R2.10-24202-MONTHLY.business.monthly_data_range"
          ruleName: "月度统计数据范围校验"
          description: "月度查询应包含截止到月初的历史数据"
          condition: "true"
          assertion: "true"
          enabled: true
          severity: "INFO"
          message: "数据来源：月度查询，基于脚本where openingtime<{endtime}的历史数据统计"

        # 校验套餐时间逻辑一致性
        - ruleId: "VGOP1-R2.10-24202-MONTHLY.business.package_time_logic"
          ruleName: "套餐时间逻辑一致性校验"
          description: "套餐失效时间应该大于等于生效时间"
          condition: "Begintime is not null and Begintime != '' and Endtime is not null and Endtime != ''"
          assertion: "Endtime >= Begintime"
          enabled: true
          severity: "WARNING"
          message: "套餐失效时间应该大于等于生效时间"

        # 校验月度统计数据范围
        - ruleId: "VGOP1-R2.10-24202-MONTHLY.business.monthly_data_range"
          ruleName: "月度统计数据范围校验"
          description: "月度查询应包含截止到月初的历史数据"
          condition: "true"
          assertion: "true"
          enabled: true
          severity: "INFO"
          message: "数据来源：月度查询，基于脚本where openingtime<{endtime}的历史数据统计"

    # 24204月接口配置 - 用户开销户操作日志月度查询（基于VGOP1-R2.10-24204.sh脚本）
    'VGOP1-R2.10-24204-MONTHLY':
      delimiter: "\\|"
      headerLine: false
      fields:
        # 第1位：日期 - VARCHAR2(8)
        - fieldIndex: 0
          fieldName: "datadate"
          description: "日期"
          type: "STRING"
          required: true
          rules:
            - ruleId: "VGOP1-R2.10-24204-MONTHLY.datadate.required"
              ruleName: "日期不能为空"
              type: "REQUIRED"
              enabled: true
              severity: "ERROR"
              message: "日期是必填项"
            - ruleId: "VGOP1-R2.10-24204-MONTHLY.datadate.format"
              ruleName: "日期格式校验"
              type: "FORMAT"
              pattern: "^\\d{6}$"
              enabled: true
              severity: "ERROR"
              message: "日期格式应为YYYYMM（6位数字）"
            - ruleId: "VGOP1-R2.10-24204-MONTHLY.datadate.length"
              ruleName: "日期长度校验"
              type: "LENGTH"
              exact: 6
              enabled: true
              severity: "ERROR"
              message: "日期长度必须为6位"

        # 第2位：用户标识 - VARCHAR2(11)
        - fieldIndex: 1
          fieldName: "phonenumber"
          description: "用户标识"
          type: "MOBILE"
          required: true
          rules:
            - ruleId: "VGOP1-R2.10-24204-MONTHLY.phonenumber.required"
              ruleName: "手机号码不能为空"
              type: "REQUIRED"
              enabled: true
              severity: "ERROR"
              message: "手机号码是必填项"
            - ruleId: "VGOP1-R2.10-24204-MONTHLY.phonenumber.format"
              ruleName: "手机号格式校验"
              type: "FORMAT"
              pattern: "^1[3-9]\\d{9}$"
              enabled: true
              severity: "ERROR"
              message: "手机号格式不正确，应为不带+86的11位数字号码"
            - ruleId: "VGOP1-R2.10-24204-MONTHLY.phonenumber.length"
              ruleName: "手机号码长度校验"
              type: "LENGTH"
              exact: 11
              enabled: true
              severity: "ERROR"
              message: "手机号码长度必须为11位"

        # 第3位：操作类型 - VARCHAR2(2)
        - fieldIndex: 2
          fieldName: "optype"
          description: "操作类型"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24204-MONTHLY.optype.enum"
              ruleName: "操作类型枚举值校验"
              type: "ENUM"
              values: ["01", "02", ""]
              enabled: true
              severity: "WARNING"
              message: "操作类型应为：01(开户)、02(销户)或空值"
            - ruleId: "VGOP1-R2.10-24204-MONTHLY.optype.length"
              ruleName: "操作类型长度校验"
              type: "LENGTH"
              exact: 2
              enabled: true
              severity: "WARNING"
              message: "操作类型长度应为2位"

        # 第4位：操作时间 - VARCHAR2(14)
        - fieldIndex: 3
          fieldName: "optime"
          description: "操作时间"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24204-MONTHLY.optime.format"
              ruleName: "操作时间格式校验"
              type: "FORMAT"
              pattern: "^(20\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])[0-5]\\d[0-5]\\d|)$"
              enabled: true
              severity: "WARNING"
              message: "操作时间格式应为YYYYMMDDHH24MISS（14位数字）或空值"
            - ruleId: "VGOP1-R2.10-24204-MONTHLY.optime.length"
              ruleName: "操作时间长度校验"
              type: "LENGTH"
              exact: 14
              enabled: true
              severity: "WARNING"
              message: "操作时间长度应为14位"

        # 第5位：接入媒介 - VARCHAR2(64)
        - fieldIndex: 4
          fieldName: "channel"
          description: "接入媒介"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24204-MONTHLY.channel.length"
              ruleName: "接入媒介长度校验"
              type: "LENGTH"
              max: 64
              enabled: true
              severity: "WARNING"
              message: "接入媒介长度不能超过64位"
            - ruleId: "VGOP1-R2.10-24204-MONTHLY.channel.format"
              ruleName: "接入媒介格式校验"
              type: "FORMAT"
              pattern: "^([a-zA-Z0-9]*|)$"
              enabled: true
              severity: "WARNING"
              message: "接入媒介应为数字和字母组合或空值"

      # 业务逻辑校验规则（基于脚本逻辑）
      businessRules:
        # 校验WHERE条件约束 - 基于脚本WHERE条件
        - ruleId: "VGOP1-R2.10-24204-MONTHLY.business.where_constraint"
          ruleName: "WHERE条件约束校验"
          description: "基于脚本WHERE条件：optime<{endtime} and optime>={starttime} and opmanner=9 and (optype='s' or optype='z')"
          condition: "phonenumber is not null and phonenumber != ''"
          assertion: "optype in ('01', '02', '')"
          enabled: true
          severity: "ERROR"
          message: "数据不符合脚本WHERE条件：手机号不为空且操作类型必须为开户或销户"

        # 校验字段转换逻辑 - 操作类型转换逻辑
        - ruleId: "VGOP1-R2.10-24204-MONTHLY.business.optype_transformation"
          ruleName: "操作类型转换逻辑校验"
          description: "基于脚本字段转换逻辑：case optype when 's' then '01' when 'z' then '02' end"
          condition: "optype is not null and optype != ''"
          assertion: "optype in ('01', '02')"
          enabled: true
          severity: "WARNING"
          message: "操作类型转换逻辑：s->01(开户)，z->02(销户)"

        # 校验固定值字段 - 接入媒介固定值
        - ruleId: "VGOP1-R2.10-24204-MONTHLY.business.fixed_value_channel"
          ruleName: "固定值字段校验"
          description: "基于脚本固定值：'' as channel"
          condition: "true"
          assertion: "channel = '' or channel is null"
          enabled: true
          severity: "WARNING"
          message: "接入媒介固定值应为空值"

        # 校验日期与操作时间的关联性
        - ruleId: "VGOP1-R2.10-24204-MONTHLY.business.date_optime_relationship"
          ruleName: "日期与操作时间关联性校验"
          description: "日期应与操作时间的年月部分一致"
          condition: "datadate is not null and datadate != '' and optime is not null and optime != ''"
          assertion: "substring(optime, 1, 6) = datadate"
          enabled: true
          severity: "WARNING"
          message: "日期应与操作时间的年月部分保持一致"

        # 校验时间范围逻辑性
        - ruleId: "VGOP1-R2.10-24204-MONTHLY.business.time_range_logic"
          ruleName: "时间范围逻辑性校验"
          description: "操作时间应在指定的月度时间范围内"
          condition: "optime is not null and optime != ''"
          assertion: "length(optime) = 14"
          enabled: true
          severity: "ERROR"
          message: "操作时间必须为14位时间格式，符合月度时间范围约束"

        # 校验用户行为一致性
        - ruleId: "VGOP1-R2.10-24204-MONTHLY.business.user_behavior_consistency"
          ruleName: "用户行为一致性校验"
          description: "用户操作类型与时间应符合业务逻辑"
          condition: "optype is not null and optype != '' and optime is not null and optime != ''"
          assertion: "optype in ('01', '02')"
          enabled: true
          severity: "WARNING"
          message: "用户行为类型应为有效的开户或销户操作"

        # 校验月度统计数据范围
        - ruleId: "VGOP1-R2.10-24204-MONTHLY.business.monthly_data_range"
          ruleName: "月度统计数据范围校验"
          description: "月度查询应包含指定时间范围内的用户操作行为"
          condition: "true"
          assertion: "true"
          enabled: true
          severity: "INFO"
          message: "数据来源：月度查询，基于脚本where optime<{endtime} and optime>={starttime}的用户操作日志统计"

        # 校验数据质量完整性
        - ruleId: "VGOP1-R2.10-24204-MONTHLY.business.data_quality_integrity"
          ruleName: "数据质量完整性校验"
          description: "确保核心字段的数据质量"
          condition: "true"
          assertion: "datadate is not null and datadate != '' and phonenumber is not null and phonenumber != ''"
          enabled: true
          severity: "ERROR"
          message: "日期和手机号码是核心必填字段，不能为空"

    # 24207月接口配置 - 实体副号用户月度查询（基于VGOP1-R2.10-24207month.sh脚本）
    'VGOP1-R2.10-24207-MONTHLY':
      delimiter: "\\|"
      headerLine: false
      fields:
        # 第1位：用户主卡号码 - VARCHAR2(16)
        - fieldIndex: 0
          fieldName: "phonenumber"
          description: "用户主卡号码"
          type: "MOBILE"
          required: true
          rules:
            - ruleId: "VGOP1-R2.10-24207-MONTHLY.phonenumber.required"
              ruleName: "主卡号码不能为空"
              type: "REQUIRED"
              enabled: true
              severity: "ERROR"
              message: "主卡号码是必填项"
            - ruleId: "VGOP1-R2.10-24207-MONTHLY.phonenumber.format"
              ruleName: "主卡号码格式校验"
              type: "FORMAT"
              pattern: "^(1[3-9]\\d{9}|00\\d{11,13})$"
              enabled: true
              severity: "ERROR"
              message: "主卡号码格式不正确，应为不带+86的11位数字号码或兼容国际号码加字冠00"
            - ruleId: "VGOP1-R2.10-24207-MONTHLY.phonenumber.length"
              ruleName: "主卡号码长度校验"
              type: "LENGTH"
              max: 16
              enabled: true
              severity: "WARNING"
              message: "主卡号码长度不能超过16位"

        # 第2位：用户副卡号码 - VARCHAR2(16)
        - fieldIndex: 1
          fieldName: "mcnnumber"
          description: "用户副卡号码"
          type: "MOBILE"
          required: true
          rules:
            - ruleId: "VGOP1-R2.10-24207-MONTHLY.mcnnumber.required"
              ruleName: "副卡号码不能为空"
              type: "REQUIRED"
              enabled: true
              severity: "ERROR"
              message: "副卡号码是必填项"
            - ruleId: "VGOP1-R2.10-24207-MONTHLY.mcnnumber.format"
              ruleName: "副卡号码格式校验"
              type: "FORMAT"
              pattern: "^(1[3-9]\\d{9}|00\\d{11,13})$"
              enabled: true
              severity: "ERROR"
              message: "副卡号码格式不正确，应为不带+86的11位数字号码或兼容国际号码加字冠00"
            - ruleId: "VGOP1-R2.10-24207-MONTHLY.mcnnumber.length"
              ruleName: "副卡号码长度校验"
              type: "LENGTH"
              max: 16
              enabled: true
              severity: "WARNING"
              message: "副卡号码长度不能超过16位"

        # 第3位：副卡IMSI号 - VARCHAR2(20)
        - fieldIndex: 2
          fieldName: "mcnImsi"
          description: "副卡IMSI号"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24207-MONTHLY.mcnimsi.length"
              ruleName: "副卡IMSI长度校验"
              type: "LENGTH"
              max: 20
              enabled: true
              severity: "WARNING"
              message: "副卡IMSI号长度不能超过20位"
            - ruleId: "VGOP1-R2.10-24207-MONTHLY.mcnimsi.format"
              ruleName: "副卡IMSI格式校验"
              type: "FORMAT"
              pattern: "^(\\d{15,20}|)$"
              enabled: true
              severity: "WARNING"
              message: "副卡IMSI应为15-20位数字或空值"

        # 第4位：业务状态 - VARCHAR2(1)
        - fieldIndex: 3
          fieldName: "businessState"
          description: "业务状态"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24207-MONTHLY.businessstate.enum"
              ruleName: "业务状态枚举值校验"
              type: "ENUM"
              values: ["0", "1", "X", ""]
              enabled: true
              severity: "WARNING"
              message: "业务状态应为：0(正常)、1(暂停)、X(特殊状态)或空值"
            - ruleId: "VGOP1-R2.10-24207-MONTHLY.businessstate.length"
              ruleName: "业务状态长度校验"
              type: "LENGTH"
              exact: 1
              enabled: true
              severity: "WARNING"
              message: "业务状态长度应为1位"

        # 第5位：副卡功能状态 - VARCHAR2(7)
        - fieldIndex: 4
          fieldName: "Numstate"
          description: "副卡功能状态"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24207-MONTHLY.numstate.length"
              ruleName: "功能状态长度校验"
              type: "LENGTH"
              exact: 7
              enabled: true
              severity: "WARNING"
              message: "功能状态长度应为7位，形如'1000000'"
            - ruleId: "VGOP1-R2.10-24207-MONTHLY.numstate.format"
              ruleName: "功能状态格式校验"
              type: "FORMAT"
              pattern: "^([01]{7}|)$"
              enabled: true
              severity: "WARNING"
              message: "功能状态应为7位二进制数字，第一位表示逻辑开关机状态"

        # 第6位：主号码归属区号 - VARCHAR2(5)
        - fieldIndex: 5
          fieldName: "Locationid"
          description: "主号码归属区号"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24207-MONTHLY.locationid.length"
              ruleName: "归属区号长度校验"
              type: "LENGTH"
              max: 5
              enabled: true
              severity: "WARNING"
              message: "归属区号长度不能超过5位，不带0"

        # 第7位：主号码省ID - NUMBER(12)
        - fieldIndex: 6
          fieldName: "BossProvinceid"
          description: "主号码省ID"
          type: "INTEGER"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24207-MONTHLY.bossprovinceid.format"
              ruleName: "省ID格式校验"
              type: "FORMAT"
              pattern: "^(\\d+|)$"
              enabled: true
              severity: "WARNING"
              message: "省ID应为数字格式，例如北京省ID为100"
            - ruleId: "VGOP1-R2.10-24207-MONTHLY.bossprovinceid.range"
              ruleName: "省ID范围校验"
              type: "RANGE"
              min: 1
              max: 2147483647
              enabled: true
              severity: "WARNING"
              message: "省ID应在有效数字范围内"

        # 第8位：开户时间 - VARCHAR2(14)
        - fieldIndex: 7
          fieldName: "openingtime"
          description: "开户时间"
          type: "STRING"
          required: false
          rules:
            - ruleId: "VGOP1-R2.10-24207-MONTHLY.openingtime.format"
              ruleName: "开户时间格式校验"
              type: "FORMAT"
              pattern: "^(20\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])([01]\\d|2[0-3])[0-5]\\d[0-5]\\d|)$"
              enabled: true
              severity: "WARNING"
              message: "开户时间格式应为YYYYMMDDHH24MISS（14位数字）或空值"
            - ruleId: "VGOP1-R2.10-24207-MONTHLY.openingtime.length"
              ruleName: "开户时间长度校验"
              type: "LENGTH"
              exact: 14
              enabled: true
              severity: "WARNING"
              message: "开户时间长度应为14位"

      # 业务逻辑校验规则（基于脚本逻辑）
      businessRules:
        # 校验WHERE条件约束 - 基于脚本WHERE条件
        - ruleId: "VGOP1-R2.10-24207-MONTHLY.business.where_constraint"
          ruleName: "WHERE条件约束校验"
          description: "基于脚本WHERE条件：openingtime<{endtime} and businessState='X'"
          condition: "phonenumber is not null and phonenumber != ''"
          assertion: "businessState = 'X' or businessState is null or businessState = ''"
          enabled: true
          severity: "ERROR"
          message: "数据不符合脚本WHERE条件：开户时间应小于结束时间且业务状态为'X'"

        # 校验UNION ALL逻辑
        - ruleId: "VGOP1-R2.10-24207-MONTHLY.business.union_all_logic"
          ruleName: "UNION ALL数据源校验"
          description: "基于脚本UNION ALL逻辑：合并mcn_sec_major和mcn_sec_major2两个表数据"
          condition: "true"
          assertion: "phonenumber is not null and phonenumber != '' and mcnnumber is not null and mcnnumber != ''"
          enabled: true
          severity: "WARNING"
          message: "UNION ALL数据源校验：主卡号码和副卡号码不能为空"

        # 校验主副卡号码关联性
        - ruleId: "VGOP1-R2.10-24207-MONTHLY.business.primary_secondary_card_relationship"
          ruleName: "主副卡号码关联性校验"
          description: "主卡号码与副卡号码应存在关联关系"
          condition: "phonenumber is not null and phonenumber != '' and mcnnumber is not null and mcnnumber != ''"
          assertion: "phonenumber != mcnnumber"
          enabled: true
          severity: "WARNING"
          message: "主卡号码与副卡号码应为不同号码"

        # 校验副卡IMSI与业务状态关联性
        - ruleId: "VGOP1-R2.10-24207-MONTHLY.business.imsi_business_state_relationship"
          ruleName: "副卡IMSI与业务状态关联性校验"
          description: "正常业务状态的副卡应该有IMSI号"
          condition: "businessState = '0'"
          assertion: "mcnImsi is not null and mcnImsi != ''"
          enabled: true
          severity: "WARNING"
          message: "正常业务状态的副卡应该有有效的IMSI号"

        # 校验功能状态逻辑一致性
        - ruleId: "VGOP1-R2.10-24207-MONTHLY.business.numstate_logic_consistency"
          ruleName: "功能状态逻辑一致性校验"
          description: "功能状态第一位表示逻辑开关机状态"
          condition: "Numstate is not null and Numstate != '' and length(Numstate) = 7"
          assertion: "substring(Numstate, 1, 1) in ('0', '1')"
          enabled: true
          severity: "WARNING"
          message: "功能状态第一位应为0(逻辑开机)或1(逻辑关机)"

        # 校验归属地一致性
        - ruleId: "VGOP1-R2.10-24207-MONTHLY.business.location_consistency"
          ruleName: "归属地一致性校验"
          description: "主号码归属区号与省ID应保持一致"
          condition: "Locationid is not null and Locationid != '' and BossProvinceid is not null and BossProvinceid != ''"
          assertion: "true"
          enabled: true
          severity: "INFO"
          message: "归属区号与省ID的一致性需要通过业务规则进一步验证"

        # 校验开户时间逻辑性
        - ruleId: "VGOP1-R2.10-24207-MONTHLY.business.openingtime_logic"
          ruleName: "开户时间逻辑性校验"
          description: "开户时间应符合业务逻辑范围"
          condition: "openingtime is not null and openingtime != ''"
          assertion: "length(openingtime) = 14"
          enabled: true
          severity: "ERROR"
          message: "开户时间必须为14位时间格式"

        # 校验特殊业务状态数据
        - ruleId: "VGOP1-R2.10-24207-MONTHLY.business.special_business_state"
          ruleName: "特殊业务状态数据校验"
          description: "脚本专门筛选业务状态为'X'的记录"
          condition: "businessState is not null and businessState != ''"
          assertion: "businessState = 'X'"
          enabled: true
          severity: "INFO"
          message: "脚本筛选条件：仅包含业务状态为'X'的特殊记录"

        # 校验月度统计数据范围
        - ruleId: "VGOP1-R2.10-24207-MONTHLY.business.monthly_data_range"
          ruleName: "月度统计数据范围校验"
          description: "月度查询应包含截止到月初的历史数据"
          condition: "true"
          assertion: "true"
          enabled: true
          severity: "INFO"
          message: "数据来源：月度查询，基于脚本where openingtime<{endtime} and businessState='X'的历史数据统计"

        # 校验数据质量完整性
        - ruleId: "VGOP1-R2.10-24207-MONTHLY.business.data_quality_integrity"
          ruleName: "数据质量完整性校验"
          description: "确保核心字段的数据质量"
          condition: "true"
          assertion: "phonenumber is not null and phonenumber != '' and mcnnumber is not null and mcnnumber != ''"
          enabled: true
          severity: "ERROR"
          message: "主卡号码和副卡号码是核心必填字段，不能为空"
