package com.vgop.service.service;

import com.vgop.service.config.TaskConfig;
import com.vgop.service.config.VgopAppConfig;
import com.vgop.service.config.VgopProperties;
import com.vgop.service.dao.DataExportMapper;
import com.vgop.service.exception.DatabaseException;
import com.vgop.service.util.DatabaseUtil;
import com.vgop.service.util.DateTimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import com.vgop.service.util.MdcUtil;

import java.io.File;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 数据导出服务
 * 负责各种业务数据的查询和导出，对应Shell脚本中的SQL查询和unload操作
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataExportService {
    
    private final DataExportMapper dataExportMapper;
    private final VgopProperties vgopProperties;
    private final VgopAppConfig appConfig;
    private final UnloadExecutorService unloadExecutorService;
    private final DatabaseUtil databaseUtil;
    
    /**
     * 根据任务配置导出接口数据
     * 
     * @param taskConfig 任务配置
     * @param dataDate 数据日期
     * @return 导出是否成功
     */
    public boolean exportInterfaceData(TaskConfig taskConfig, String dataDate) {
        String interfaceId = taskConfig.getInterfaceId();
        
        try {
            return MdcUtil.executeWithContext(interfaceId, dataDate, 1, () -> {
                // 默认版本号为1
                ExportResult result = exportData(taskConfig, dataDate, 1);
                return result.isSuccess();
            });
        } catch (Exception e) {
            log.error("接口数据导出失败: interfaceId=" + interfaceId, e);
            return false;
        }
    }
    
    /**
     * 导出所有日常任务数据
     * 
     * @param dataDate 数据日期
     * @return 成功导出的任务数量
     */
    public int exportAllDailyData(String dataDate) {
        try {
            return MdcUtil.executeWithBatchContext("exportAllDailyData", dataDate, () -> {
                log.info("开始导出所有日常任务数据: dataDate={}", dataDate);
                int successCount = 0;
                
                // 从配置中获取所有启用的日常任务
                List<TaskConfig> dailyTasks = appConfig.getEnabledDailyTasks();
                if (dailyTasks.isEmpty()) {
                    log.warn("没有配置启用的日常任务");
                    return 0;
                }
                
                // 遍历所有任务并执行导出
                for (TaskConfig taskConfig : dailyTasks) {
                    try {
                        log.info("开始导出任务: {}", taskConfig.getInterfaceId());
                        boolean success = exportInterfaceData(taskConfig, dataDate);
                        if (success) {
                            successCount++;
                            log.info("成功导出任务: {}", taskConfig.getInterfaceId());
                        } else {
                            log.error("导出任务失败: {}", taskConfig.getInterfaceId());
                        }
                    } catch (Exception e) {
                        log.error("导出任务异常: " + taskConfig.getInterfaceId(), e);
                    }
                }
                
                log.info("日常任务数据导出完成: 总任务数={}, 成功任务数={}", dailyTasks.size(), successCount);
                return successCount;
            });
        } catch (Exception e) {
            log.error("批量导出日常任务数据失败", e);
            return 0;
        }
    }
    
    /**
     * 根据任务配置导出数据
     * 
     * @param taskConfig 任务配置
     * @param dataDate 数据日期
     * @param revision 版本号
     * @return 导出结果
     */
    public ExportResult exportData(TaskConfig taskConfig, String dataDate, int revision) {
        String interfaceId = taskConfig.getInterfaceId();
        
        // 设置MDC上下文，确保该线程后续的所有日志都包含interfaceId信息
        MdcUtil.setInterfaceContext(interfaceId, dataDate, revision);
        MdcUtil.setOperationType("export");
        
        try {
            log.info("开始导出接口数据: interfaceId={}, dataDate={}, revision={}", interfaceId, dataDate, revision);
            // 获取导出配置
            TaskConfig.ExportConfig exportConfig = taskConfig.getExport();
            if (exportConfig == null) {
                return new ExportResult(interfaceId, false, "导出配置为空");
            }
            
            if (exportConfig.getSqlTemplate() == null) {
                return new ExportResult(interfaceId, false, "SQL模板为空");
            }
            
            if (exportConfig.getTempFileNameTemplate() == null) {
                return new ExportResult(interfaceId, false, "临时文件名模板为空");
            }
            
            // **特殊处理：VGOP1-R2.11-24101业务分析任务需要先调用存储过程**
            if ("VGOP1-R2.11-24101".equals(interfaceId)) {
                log.info("检测到VGOP业务分析任务，执行存储过程调用: {}", interfaceId);
                
                try {
                    // 调用业务分析存储过程：bmssp_VGOP_banalyse
                    // 根据原始脚本VGOP1-R2.11-24101.sh，参数为：debugFile, traceFlag, taskId
                    String debugFile = ""; // 调试文件路径，在Java环境中可以为空
                    String traceFlag = "0"; // 默认值
                    String taskId = dataDate; // 任务ID使用数据日期
                    
                    log.info("调用存储过程 bmssp_VGOP_banalyse: debugFile={}, traceFlag={}, taskId={}", debugFile, traceFlag, taskId);
                    
                    // **关键修改：使用dbaccess命令行方式执行存储过程，确保与Shell脚本执行环境一致**
                    boolean storedProcSuccess = executeStoredProcedureViaDbAccess(debugFile, traceFlag, taskId);
                    
                    if (!storedProcSuccess) {
                        log.error("存储过程 bmssp_VGOP_banalyse 执行失败");
                        return new ExportResult(interfaceId, false, "存储过程执行失败");
                    }
                    
                    log.info("存储过程 bmssp_VGOP_banalyse 执行完成，继续执行数据导出");
                    
                } catch (Exception e) {
                    log.error("调用业务分析存储过程失败: interfaceId={}, dataDate={}", interfaceId, dataDate, e);
                    return new ExportResult(interfaceId, false, "存储过程调用失败: " + e.getMessage());
                }
            }
            
            // 替换SQL模板中的参数
            String sql = exportConfig.getSqlTemplate()
                    .replace("{dataDate}", dataDate)
                    .replace("{revision}", String.valueOf(revision));
            
            // 处理日期范围占位符（用于日常任务）
            if (taskConfig.getTaskType().equals("daily")) {
                // 对于日常任务，计算前一天到当前数据日期的时间范围
                LocalDate currentDate = LocalDate.parse(dataDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
                String previousDay = currentDate.minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                String startTime = previousDay + "000000";
                String endTime = dataDate + "000000";
                
                sql = sql.replace("{starttime}", startTime)
                        .replace("{endtime}", endTime)
                        .replace("{previousDay}", previousDay);
            }
            
            // 处理文件名模板中的占位符
            String tempFileName = exportConfig.getTempFileNameTemplate()
                    .replace("{dataDate}", dataDate)
                    .replace("{interfaceId}", interfaceId);
            
            // 如果是日常任务，还需要处理previousDay占位符
            if (taskConfig.getTaskType().equals("daily")) {
                LocalDate currentDate = LocalDate.parse(dataDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
                String previousDay = currentDate.minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                tempFileName = tempFileName.replace("{previousDay}", previousDay);
            }
            
            log.debug("构建UNLOAD SQL: {}", sql);
            
            // 获取导出路径
            String exportRoot = vgopProperties.getExportPath();
            String cycleType = taskConfig.getTaskType();
            
            // 统一日期处理逻辑：对于日统计任务，导出目录应使用前一天日期（与Shell脚本逻辑保持一致）
            String exportDirDate = dataDate;
            if (taskConfig.getTaskType().equals("daily")) {
                exportDirDate = DateTimeUtil.calculateBeforeDay(dataDate);
                log.debug("日统计任务导出目录使用前一天日期: {} -> {}", dataDate, exportDirDate);
            }
            
            String exportPath = String.format("%s/%s/%s/", exportRoot, exportDirDate, cycleType);
            
            // 确保目录存在
            File exportDir = new File(exportPath);
            if (!exportDir.exists()) {
                boolean created = exportDir.mkdirs();
                log.debug("创建导出目录: {}, 结果: {}", exportPath, created);
            }
            
            String tempFilePath = exportPath + tempFileName;
            
            log.debug("临时文件路径: {}", tempFilePath);
            
            // 使用UNLOAD命令直接导出数据
            String delimiter = vgopProperties.getDatabase().getDefaultColumnSeparator();
            boolean success = unloadExecutorService.executeUnload(sql, tempFilePath, delimiter);
            
            if (success) {
                // 统计文件行数作为记录数
                File outputFile = new File(tempFilePath);
                long recordCount = countFileLines(outputFile);
                
                log.info("接口数据导出成功: interfaceId={}, 记录数={}", interfaceId, recordCount);
                return new ExportResult(interfaceId, true, 1, (int) recordCount);
            } else {
                return new ExportResult(interfaceId, false, "UNLOAD执行失败");
            }
            
        } catch (Exception e) {
            log.error("接口数据导出失败: interfaceId=" + interfaceId, e);
            return new ExportResult(interfaceId, false, e.getMessage());
        } finally {
            // 清理MDC上下文，避免内存泄漏
            MdcUtil.clearInterfaceContext();
            MdcUtil.clearOperationType();
        }
    }
    
    /**
     * 导出业务分析数据
     * 对应VGOP1-R2.11-24101.sh脚本
     */
    public String exportBanalyseData(String actionInstanceId, String imagePath, String dateId,
                                   String traceFlag, String sourceDbName) {
        try {
            String beforeDay = DateTimeUtil.calculateBeforeDay(dateId);
            
            log.info("执行业务分析存储过程: beforeDay={}", beforeDay);
            
            // 调用存储过程
            dataExportMapper.callBanalyseStoredProcedure(beforeDay, traceFlag, sourceDbName);
            
            // 构建UNLOAD SQL
            String sql = "select '' as firstField, phonenumber, msisdn, msisdn, mcnnumber, mcnnumber, " +
                        "provinceid, phonenumber, stype_total, stype_group, stype_2G, stype_3G, stype_4G, stype_5G, " +
                        "locationid, provinceid, cityid, areaid, regionid, businessState, serviceid " +
                        "from vgop_metrics_history where stattime = '" + beforeDay + "'";
            
            String tmpFileName = String.format("i_10000_%s_VGOP1-R2.11-24101.unl", beforeDay);
            String dataPath = String.format("%s/VGOPdata/datafile/%s/day/", imagePath, beforeDay);
            String unloadFileName = dataPath + tmpFileName;
            
            // 确保目录存在
            File dataDir = new File(dataPath);
            if (!dataDir.exists()) {
                dataDir.mkdirs();
            }
            
            // 使用UNLOAD命令导出数据
            String delimiter = vgopProperties.getDatabase().getDefaultColumnSeparator();
            boolean success = unloadExecutorService.executeUnload(sql, unloadFileName, delimiter);
            
            if (success) {
                File outputFile = new File(unloadFileName);
                long recordCount = countFileLines(outputFile);
                log.info("业务分析数据导出完成: {} 条记录", recordCount);
            } else {
                throw new DatabaseException("业务分析数据UNLOAD执行失败");
            }
            
            return unloadFileName;
            
        } catch (Exception e) {
            log.error("业务分析数据导出失败", e);
            throw new DatabaseException("业务分析数据导出失败", e);
        }
    }
    
    /**
     * 导出每日新增主号用户信息
     * 对应VGOP1-R2.10-24201day.sh脚本
     */
    public String exportDailyNewMajorUsers(String actionInstanceId, String imagePath, String dateId) {
        try {
            String[] timeRange = DateTimeUtil.calculateDayTimeRange(dateId);
            String startTime = timeRange[0];
            String endTime = timeRange[1];
            String beforeDay = DateTimeUtil.calculateBeforeDay(dateId);
            
            log.info("导出每日新增主号用户: startTime={}, endTime={}", startTime, endTime);
            
            // 构建UNLOAD SQL
            String sql = "select phonenumber,mcnnumber,locationid,serviceid,userid,channelid,optime,businessState,provinceid," +
                        "cityid,areaid,regionid,openingtime,operateAcct,openingAcct,mdn,mcnImsi " +
                        "from mcn_user_major where openingtime>='" + startTime + "' and openingtime<'" + endTime + "'";
            
            String tmpFileName = String.format("a_10000_%s_VGOP1-R2.10-24201.unl", beforeDay);
            String dataPath = String.format("%s/VGOPdata/datafile/%s/day/", imagePath, beforeDay);
            String unloadFileName = dataPath + tmpFileName;
            
            // 确保目录存在
            File dataDir = new File(dataPath);
            if (!dataDir.exists()) {
                dataDir.mkdirs();
            }
            
            // 使用UNLOAD命令导出数据
            String delimiter = vgopProperties.getDatabase().getDefaultColumnSeparator();
            boolean success = unloadExecutorService.executeUnload(sql, unloadFileName, delimiter);
            
            if (success) {
                File outputFile = new File(unloadFileName);
                long recordCount = countFileLines(outputFile);
                log.info("每日新增主号用户导出完成: {} 条记录", recordCount);
            } else {
                throw new DatabaseException("每日新增主号用户UNLOAD执行失败");
            }
            
            return unloadFileName;
            
        } catch (Exception e) {
            log.error("每日新增主号用户导出失败", e);
            throw new DatabaseException("每日新增主号用户导出失败", e);
        }
    }
    
    /**
     * 导出主号用户全量快照（月统计）
     * 对应VGOP1-R2.10-24201month.sh脚本
     */
    public String exportMajorUsersSnapshot(String actionInstanceId, String imagePath, String dateId) {
        try {
            String beforeMonth = DateTimeUtil.calculateBeforeMonth(dateId);
            String endDate = beforeMonth + "31235959"; // 上月最后一天
            
            log.info("导出主号用户全量快照: endDate={}", endDate);
            
            // 构建UNLOAD SQL
            String sql = "select phonenumber,mcnnumber,locationid,serviceid,userid,channelid,optime,businessState,provinceid," +
                        "cityid,areaid,regionid,openingtime,operateAcct,openingAcct,mdn,mcnImsi " +
                        "from mcn_user_major where openingtime<'" + endDate + "'";
            
            String tmpFileName = String.format("i_10000_%s_VGOP1-R2.10-24201month.unl", beforeMonth);
            String dataPath = String.format("%s/VGOPdata/datafile/%s/month/", imagePath, beforeMonth);
            String unloadFileName = dataPath + tmpFileName;
            
            File dataDir = new File(dataPath);
            if (!dataDir.exists()) {
                dataDir.mkdirs();
            }
            
            // 使用UNLOAD命令导出数据
            String delimiter = vgopProperties.getDatabase().getDefaultColumnSeparator();
            boolean success = unloadExecutorService.executeUnload(sql, unloadFileName, delimiter);
            
            if (success) {
                File outputFile = new File(unloadFileName);
                long recordCount = countFileLines(outputFile);
                log.info("主号用户全量快照导出完成: {} 条记录", recordCount);
            } else {
                throw new DatabaseException("主号用户全量快照UNLOAD执行失败");
            }
            
            return unloadFileName;
            
        } catch (Exception e) {
            log.error("主号用户全量快照导出失败", e);
            throw new DatabaseException("主号用户全量快照导出失败", e);
        }
    }
    
    /**
     * 导出副号用户全量快照（月统计）
     * 对应VGOP1-R2.10-24202month.sh脚本
     */
    public String exportMinorUsersSnapshot(String actionInstanceId, String imagePath, String dateId) {
        try {
            String beforeMonth = DateTimeUtil.calculateBeforeMonth(dateId);
            String endDate = beforeMonth + "31235959";
            
            log.info("导出副号用户全量快照: endDate={}", endDate);
            
            // 构建UNLOAD SQL
            String sql = "select phonenumber,mcnnumber,mcnImsi,businessState,Numstate,Locationid,BossProvinceid,openingtime " +
                        "from mcn_sec_major where openingtime<'" + endDate + "' and mcnnums in ('1','2','3')";
            
            String tmpFileName = String.format("i_10000_%s_VGOP1-R2.10-24202month.unl", beforeMonth);
            String dataPath = String.format("%s/VGOPdata/datafile/%s/month/", imagePath, beforeMonth);
            String unloadFileName = dataPath + tmpFileName;
            
            File dataDir = new File(dataPath);
            if (!dataDir.exists()) {
                dataDir.mkdirs();
            }
            
            // 使用UNLOAD命令导出数据
            String delimiter = vgopProperties.getDatabase().getDefaultColumnSeparator();
            boolean success = unloadExecutorService.executeUnload(sql, unloadFileName, delimiter);
            
            if (success) {
                File outputFile = new File(unloadFileName);
                long recordCount = countFileLines(outputFile);
                log.info("副号用户全量快照导出完成: {} 条记录", recordCount);
            } else {
                throw new DatabaseException("副号用户全量快照UNLOAD执行失败");
            }
            
            return unloadFileName;
            
        } catch (Exception e) {
            log.error("副号用户全量快照导出失败", e);
            throw new DatabaseException("副号用户全量快照导出失败", e);
        }
    }
    
    /**
     * 导出月度操作日志
     * 对应VGOP1-R2.10-24204.sh脚本
     */
    public String exportMonthlyOpLogs(String actionInstanceId, String imagePath, String dateId) {
        try {
            String[] timeRange = DateTimeUtil.calculateMonthTimeRange(dateId);
            String startTime = timeRange[0];
            String endTime = timeRange[1];
            String beforeMonth = DateTimeUtil.calculateBeforeMonth(dateId);
            
            log.info("导出月度操作日志: startTime={}, endTime={}", startTime, endTime);
            
            // 构建UNLOAD SQL
            String sql = "select phonenumber,mcnnumber,optime,operatetype,operateAcct " +
                        "from mcn_oplog where optime>='" + startTime + "' and optime<'" + endTime + "'";
            
            String tmpFileName = String.format("i_10000_%s_VGOP1-R2.10-24204.unl", beforeMonth);
            String dataPath = String.format("%s/VGOPdata/datafile/%s/month/", imagePath, beforeMonth);
            String unloadFileName = dataPath + tmpFileName;
            
            File dataDir = new File(dataPath);
            if (!dataDir.exists()) {
                dataDir.mkdirs();
            }
            
            // 使用UNLOAD命令导出数据
            String delimiter = vgopProperties.getDatabase().getDefaultColumnSeparator();
            boolean success = unloadExecutorService.executeUnload(sql, unloadFileName, delimiter);
            
            if (success) {
                File outputFile = new File(unloadFileName);
                long recordCount = countFileLines(outputFile);
                log.info("月度操作日志导出完成: {} 条记录", recordCount);
            } else {
                throw new DatabaseException("月度操作日志UNLOAD执行失败");
            }
            
            return unloadFileName;
            
        } catch (Exception e) {
            log.error("月度操作日志导出失败", e);
            throw new DatabaseException("月度操作日志导出失败", e);
        }
    }
    
    /**
     * 导出实体副号用户全量快照
     * 对应VGOP1-R2.10-24207month.sh脚本
     */
    public String exportSecUsersSnapshot(String actionInstanceId, String imagePath, String dateId) {
        try {
            String beforeMonth = DateTimeUtil.calculateBeforeMonth(dateId);
            String endDate = beforeMonth + "31235959";
            
            log.info("导出实体副号用户全量快照: beforeMonth={}", beforeMonth);
            
            // 构建UNLOAD SQL，使用UNION语句合并两个表的数据
            String sql = "select phonenumber,mcnnumber,mcnImsi,businessState,Numstate,Locationid,BossProvinceid,openingtime from (" +
                        "(select phonenumber,mcnnumber,mcnImsi,businessState,Numstate,Locationid,BossProvinceid,openingtime " +
                        "from mcn_sec_major where openingtime<'" + endDate + "' and businessState='X') " +
                        "union all " +
                        "(select phonenumber,mcnnumber,mcnImsi,businessState,Numstate,Locationid,BossProvinceid,openingtime " +
                        "from mcn_sec_major2 where openingtime<'" + endDate + "' and businessState='X'))";
            
            String tmpFileName = String.format("i_10000_%s_VGOP1-R2.10-24207month.unl", beforeMonth);
            String dataPath = String.format("%s/VGOPdata/datafile/%s/month/", imagePath, beforeMonth);
            String unloadFileName = dataPath + tmpFileName;
            
            File dataDir = new File(dataPath);
            if (!dataDir.exists()) {
                dataDir.mkdirs();
            }
            
            // 使用UNLOAD命令导出数据
            String delimiter = vgopProperties.getDatabase().getDefaultColumnSeparator();
            boolean success = unloadExecutorService.executeUnload(sql, unloadFileName, delimiter);
            
            if (success) {
                File outputFile = new File(unloadFileName);
                long recordCount = countFileLines(outputFile);
                log.info("实体副号用户全量快照导出完成: {} 条记录", recordCount);
            } else {
                throw new DatabaseException("实体副号用户全量快照UNLOAD执行失败");
            }
            
            return unloadFileName;
            
        } catch (Exception e) {
            log.error("实体副号用户全量快照导出失败", e);
            throw new DatabaseException("实体副号用户全量快照导出失败", e);
        }
    }

    /**
     * 导出副号用户信息
     * 对应VGOP1-R2.10-24202day.sh脚本
     */
    public String exportMinorUsers(String actionInstanceId, String imagePath, String dateId) {
        try {
            String beforeDay = DateTimeUtil.calculateBeforeDay(dateId);
            
            log.info("导出副号用户信息: beforeDay={}", beforeDay);
            
            // 构建UNLOAD SQL
            String sql = "select phonenumber,mcnnumber,mcnImsi,businessState,Numstate,Locationid,BossProvinceid,openingtime " +
                        "from mcn_sec_major where mcnnums in ('1','2','3')";
            
            String tmpFileName = String.format("i_10000_%s_VGOP1-R2.10-24202.unl", beforeDay);
            String dataPath = String.format("%s/VGOPdata/datafile/%s/day/", imagePath, beforeDay);
            String unloadFileName = dataPath + tmpFileName;
            
            File dataDir = new File(dataPath);
            if (!dataDir.exists()) {
                dataDir.mkdirs();
            }
            
            // 使用UNLOAD命令导出数据
            String delimiter = vgopProperties.getDatabase().getDefaultColumnSeparator();
            boolean success = unloadExecutorService.executeUnload(sql, unloadFileName, delimiter);
            
            if (success) {
                File outputFile = new File(unloadFileName);
                long recordCount = countFileLines(outputFile);
                log.info("副号用户信息导出完成: {} 条记录", recordCount);
            } else {
                throw new DatabaseException("副号用户信息UNLOAD执行失败");
            }
            
            return unloadFileName;
            
        } catch (Exception e) {
            log.error("副号用户信息导出失败", e);
            throw new DatabaseException("副号用户信息导出失败", e);
        }
    }
    
    /**
     * 导出每日用户活动日志
     * 对应VGOP1-R2.10-24203.sh脚本
     */
    public String exportDailyUserActivityLogs(String actionInstanceId, String imagePath, String dateId) {
        try {
            String[] timeRange = DateTimeUtil.calculateDayTimeRange(dateId);
            String startTime = timeRange[0];
            String endTime = timeRange[1];
            String beforeDay = DateTimeUtil.calculateBeforeDay(dateId);
            
            log.info("导出每日用户活动日志: startTime={}, endTime={}", startTime, endTime);
            
            // 构建UNLOAD SQL，与shell脚本保持一致
            String sql = "select '',phonenumber,type,optime,version from ( " +
                        "select phonenumber,1 as type,logintime as optime,version from mcn_apploginlog where logintime>='" + startTime + "' " +
                        "and logintime<'" + endTime + "' " +
                        "union all " +
                        "select phonenumber,2 as type,optime,'' as version from mcn_oplog where optime>='" + startTime + "' and optime<'" + endTime + "' and opmanner=4)";
            
            String tmpFileName = String.format("a_10000_%s_VGOP1-R2.10-24203.unl", beforeDay);
            String dataPath = String.format("%s/VGOPdata/datafile/%s/day/", imagePath, beforeDay);
            String unloadFileName = dataPath + tmpFileName;
            
            File dataDir = new File(dataPath);
            if (!dataDir.exists()) {
                dataDir.mkdirs();
            }
            
            // 使用UNLOAD命令导出数据
            String delimiter = vgopProperties.getDatabase().getDefaultColumnSeparator();
            boolean success = unloadExecutorService.executeUnload(sql, unloadFileName, delimiter);
            
            if (success) {
                File outputFile = new File(unloadFileName);
                long recordCount = countFileLines(outputFile);
                log.info("每日用户活动日志导出完成: {} 条记录", recordCount);
            } else {
                throw new DatabaseException("每日用户活动日志UNLOAD执行失败");
            }
            
            return unloadFileName;
            
        } catch (Exception e) {
            log.error("每日用户活动日志导出失败", e);
            throw new DatabaseException("每日用户活动日志导出失败", e);
        }
    }
    
    /**
     * 导出每日通话话单记录
     * 对应VGOP1-R2.10-24205.sh脚本
     */
    public String exportDailyCallRecords(String actionInstanceId, String imagePath, String dateId) {
        try {
            String[] timeRange = DateTimeUtil.calculateDayTimeRange(dateId);
            String startTime = timeRange[0];
            String endTime = timeRange[1];
            String beforeDay = DateTimeUtil.calculateBeforeDay(dateId);
            
            log.info("导出每日通话话单记录: startTime={}, endTime={}", startTime, endTime);
            
            // 构建UNLOAD SQL
            String sql = "select phonenumber,mcnnumber,callstarttime,callendtime,calltype,calledpartynum,duration " +
                        "from mcn_calllog where callstarttime>='" + startTime + "' and callstarttime<'" + endTime + "'";
            
            String tmpFileName = String.format("a_10000_%s_VGOP1-R2.10-24205.unl", beforeDay);
            String dataPath = String.format("%s/VGOPdata/datafile/%s/day/", imagePath, beforeDay);
            String unloadFileName = dataPath + tmpFileName;
            
            File dataDir = new File(dataPath);
            if (!dataDir.exists()) {
                dataDir.mkdirs();
            }
            
            // 使用UNLOAD命令导出数据
            String delimiter = vgopProperties.getDatabase().getDefaultColumnSeparator();
            boolean success = unloadExecutorService.executeUnload(sql, unloadFileName, delimiter);
            
            if (success) {
                File outputFile = new File(unloadFileName);
                long recordCount = countFileLines(outputFile);
                log.info("每日通话话单记录导出完成: {} 条记录", recordCount);
            } else {
                throw new DatabaseException("每日通话话单记录UNLOAD执行失败");
            }
            
            return unloadFileName;
            
        } catch (Exception e) {
            log.error("每日通话话单记录导出失败", e);
            throw new DatabaseException("每日通话话单记录导出失败", e);
        }
    }
    
    /**
     * 导出每日短信日志
     * 对应VGOP1-R2.10-24206.sh脚本
     */
    public String exportDailySmsLogs(String actionInstanceId, String imagePath, String dateId) {
        try {
            String[] timeRange = DateTimeUtil.calculateDayTimeRange(dateId);
            String startTime = timeRange[0];
            String endTime = timeRange[1];
            String beforeDay = DateTimeUtil.calculateBeforeDay(dateId);
            
            log.info("导出每日短信日志: startTime={}, endTime={}", startTime, endTime);
            
            // 构建UNLOAD SQL
            String sql = "select phonenumber,mcnnumber,smstime,smstype,destinationnum,messagecontent " +
                        "from mcn_smslog where smstime>='" + startTime + "' and smstime<'" + endTime + "'";
            
            String tmpFileName = String.format("a_10000_%s_VGOP1-R2.10-24206.unl", beforeDay);
            String dataPath = String.format("%s/VGOPdata/datafile/%s/day/", imagePath, beforeDay);
            String unloadFileName = dataPath + tmpFileName;
            
            File dataDir = new File(dataPath);
            if (!dataDir.exists()) {
                dataDir.mkdirs();
            }
            
            // 使用UNLOAD命令导出数据
            String delimiter = vgopProperties.getDatabase().getDefaultColumnSeparator();
            boolean success = unloadExecutorService.executeUnload(sql, unloadFileName, delimiter);
            
            if (success) {
                File outputFile = new File(unloadFileName);
                long recordCount = countFileLines(outputFile);
                log.info("每日短信日志导出完成: {} 条记录", recordCount);
            } else {
                throw new DatabaseException("每日短信日志UNLOAD执行失败");
            }
            
            return unloadFileName;
            
        } catch (Exception e) {
            log.error("每日短信日志导出失败", e);
            throw new DatabaseException("每日短信日志导出失败", e);
        }
    }
    
    /**
     * 导出每日新增实体副号用户信息
     * 对应VGOP1-R2.10-24207day.sh脚本
     */
    public String exportDailyNewSecUsers(String actionInstanceId, String imagePath, String dateId) {
        try {
            String[] timeRange = DateTimeUtil.calculateDayTimeRange(dateId);
            String startTime = timeRange[0];
            String endTime = timeRange[1];
            String beforeDay = DateTimeUtil.calculateBeforeDay(dateId);
            
            log.info("导出每日新增实体副号用户: startTime={}, endTime={}", startTime, endTime);
            
            // 构建UNLOAD SQL
            String sql = "select phonenumber,mcnnumber,mcnImsi,businessState,Numstate,Locationid,BossProvinceid,openingtime " +
                        "from mcn_sec_major where openingtime>='" + startTime + "' and openingtime<'" + endTime + "' and businessState='X'";
            
            String tmpFileName = String.format("a_10000_%s_VGOP1-R2.10-24207.unl", beforeDay);
            String dataPath = String.format("%s/VGOPdata/datafile/%s/day/", imagePath, beforeDay);
            String unloadFileName = dataPath + tmpFileName;
            
            File dataDir = new File(dataPath);
            if (!dataDir.exists()) {
                dataDir.mkdirs();
            }
            
            // 使用UNLOAD命令导出数据
            String delimiter = vgopProperties.getDatabase().getDefaultColumnSeparator();
            boolean success = unloadExecutorService.executeUnload(sql, unloadFileName, delimiter);
            
            if (success) {
                File outputFile = new File(unloadFileName);
                long recordCount = countFileLines(outputFile);
                log.info("每日新增实体副号用户导出完成: {} 条记录", recordCount);
            } else {
                throw new DatabaseException("每日新增实体副号用户UNLOAD执行失败");
            }
            
            return unloadFileName;
            
        } catch (Exception e) {
            log.error("每日新增实体副号用户导出失败", e);
            throw new DatabaseException("每日新增实体副号用户导出失败", e);
        }
    }
    
    /**
     * 导出维表数据 - 服务类型
     * 对应VGOP1-R2.13-24301.sh脚本
     */
    public String exportServiceTypes(String actionInstanceId, String imagePath, String dateId) {
        try {
            String beforeDay = DateTimeUtil.calculateBeforeDay(dateId);
            
            log.info("导出维表 - 服务类型: beforeDay={}", beforeDay);
            
            // 构建UNLOAD SQL
            String sql = "select ServID,ServName from vgop_servtype";
            
            String tmpFileName = String.format("i_10000_%s_VGOP1-R2.13-24301.unl", beforeDay);
            String dataPath = String.format("%s/VGOPdata/datafile/%s/day/", imagePath, beforeDay);
            String unloadFileName = dataPath + tmpFileName;
            
            File dataDir = new File(dataPath);
            if (!dataDir.exists()) {
                dataDir.mkdirs();
            }
            
            // 使用UNLOAD命令导出数据
            String delimiter = vgopProperties.getDatabase().getDefaultColumnSeparator();
            boolean success = unloadExecutorService.executeUnload(sql, unloadFileName, delimiter);
            
            if (success) {
                File outputFile = new File(unloadFileName);
                long recordCount = countFileLines(outputFile);
                log.info("服务类型维表导出完成: {} 条记录", recordCount);
            } else {
                throw new DatabaseException("服务类型维表UNLOAD执行失败");
            }
            
            return unloadFileName;
            
        } catch (Exception e) {
            log.error("服务类型维表导出失败", e);
            throw new DatabaseException("服务类型维表导出失败", e);
        }
    }
    
    /**
     * 导出维表数据 - 渠道信息
     * 对应VGOP1-R2.13-24302.sh脚本
     */
    public String exportChannelInfo(String actionInstanceId, String imagePath, String dateId) {
        try {
            String beforeDay = DateTimeUtil.calculateBeforeDay(dateId);
            
            log.info("导出维表 - 渠道信息: beforeDay={}", beforeDay);
            
            // 构建UNLOAD SQL
            String sql = "select channelcode,channelname from vgop_channel";
            
            String tmpFileName = String.format("i_10000_%s_VGOP1-R2.13-24302.unl", beforeDay);
            String dataPath = String.format("%s/VGOPdata/datafile/%s/day/", imagePath, beforeDay);
            String unloadFileName = dataPath + tmpFileName;
            
            File dataDir = new File(dataPath);
            if (!dataDir.exists()) {
                dataDir.mkdirs();
            }
            
            // 使用UNLOAD命令导出数据
            String delimiter = vgopProperties.getDatabase().getDefaultColumnSeparator();
            boolean success = unloadExecutorService.executeUnload(sql, unloadFileName, delimiter);
            
            if (success) {
                File outputFile = new File(unloadFileName);
                long recordCount = countFileLines(outputFile);
                log.info("渠道信息维表导出完成: {} 条记录", recordCount);
            } else {
                throw new DatabaseException("渠道信息维表UNLOAD执行失败");
            }
            
            return unloadFileName;
            
        } catch (Exception e) {
            log.error("渠道信息维表导出失败", e);
            throw new DatabaseException("渠道信息维表导出失败", e);
        }
    }
    
    /**
     * 导出维表数据 - 关机原因
     * 对应VGOP1-R2.13-24303.sh脚本
     */
    public String exportShutdownReasons(String actionInstanceId, String imagePath, String dateId) {
        try {
            String beforeDay = DateTimeUtil.calculateBeforeDay(dateId);
            
            log.info("导出维表 - 关机原因: beforeDay={}", beforeDay);
            
            // 构建UNLOAD SQL
            String sql = "select shutdown,shutdownname from vgop_shutdown";
            
            String tmpFileName = String.format("i_10000_%s_VGOP1-R2.13-24303.unl", beforeDay);
            String dataPath = String.format("%s/VGOPdata/datafile/%s/day/", imagePath, beforeDay);
            String unloadFileName = dataPath + tmpFileName;
            
            File dataDir = new File(dataPath);
            if (!dataDir.exists()) {
                dataDir.mkdirs();
            }
            
            // 使用UNLOAD命令导出数据
            String delimiter = vgopProperties.getDatabase().getDefaultColumnSeparator();
            boolean success = unloadExecutorService.executeUnload(sql, unloadFileName, delimiter);
            
            if (success) {
                File outputFile = new File(unloadFileName);
                long recordCount = countFileLines(outputFile);
                log.info("关机原因维表导出完成: {} 条记录", recordCount);
            } else {
                throw new DatabaseException("关机原因维表UNLOAD执行失败");
            }
            
            return unloadFileName;
            
        } catch (Exception e) {
            log.error("关机原因维表导出失败", e);
            throw new DatabaseException("关机原因维表导出失败", e);
        }
    }
    
    /**
     * 导出维表数据 - MCN类型
     * 对应VGOP1-R2.13-24304.sh脚本
     */
    public String exportMcnTypes(String actionInstanceId, String imagePath, String dateId) {
        try {
            String beforeDay = DateTimeUtil.calculateBeforeDay(dateId);
            
            log.info("导出维表 - MCN类型: beforeDay={}", beforeDay);
            
            // 构建UNLOAD SQL
            String sql = "select mcntype,mcntypename from vgop_mcntype";
            
            String tmpFileName = String.format("i_10000_%s_VGOP1-R2.13-24304.unl", beforeDay);
            String dataPath = String.format("%s/VGOPdata/datafile/%s/day/", imagePath, beforeDay);
            String unloadFileName = dataPath + tmpFileName;
            
            File dataDir = new File(dataPath);
            if (!dataDir.exists()) {
                dataDir.mkdirs();
            }
            
            // 使用UNLOAD命令导出数据
            String delimiter = vgopProperties.getDatabase().getDefaultColumnSeparator();
            boolean success = unloadExecutorService.executeUnload(sql, unloadFileName, delimiter);
            
            if (success) {
                File outputFile = new File(unloadFileName);
                long recordCount = countFileLines(outputFile);
                log.info("MCN类型维表导出完成: {} 条记录", recordCount);
            } else {
                throw new DatabaseException("MCN类型维表UNLOAD执行失败");
            }
            
            return unloadFileName;
            
        } catch (Exception e) {
            log.error("MCN类型维表导出失败", e);
            throw new DatabaseException("MCN类型维表导出失败", e);
        }
    }

    /**
     * 统计文件行数
     */
    private long countFileLines(File file) {
        if (!file.exists()) {
            return 0;
        }
        
        try (java.io.BufferedReader reader = new java.io.BufferedReader(new java.io.FileReader(file))) {
            long lines = 0;
            while (reader.readLine() != null) {
                lines++;
            }
            return lines;
        } catch (Exception e) {
            log.warn("统计文件行数失败: {}", file.getAbsolutePath(), e);
            return 0;
        }
    }
    
    /**
     * 数据导出结果类
     */
    public static class ExportResult {
        private String interfaceId;
        private boolean success;
        private int fileCount;
        private int totalRows;
        private String errorMessage;
        
        public ExportResult() {
        }
        
        public ExportResult(String interfaceId, boolean success, int fileCount, int totalRows) {
            this.interfaceId = interfaceId;
            this.success = success;
            this.fileCount = fileCount;
            this.totalRows = totalRows;
        }
        
        public ExportResult(String interfaceId, boolean success, String errorMessage) {
            this.interfaceId = interfaceId;
            this.success = success;
            this.errorMessage = errorMessage;
            this.fileCount = 0;
            this.totalRows = 0;
        }
        
        public String getInterfaceId() {
            return interfaceId;
        }
        
        public void setInterfaceId(String interfaceId) {
            this.interfaceId = interfaceId;
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public void setSuccess(boolean success) {
            this.success = success;
        }
        
        public int getFileCount() {
            return fileCount;
        }
        
        public void setFileCount(int fileCount) {
            this.fileCount = fileCount;
        }
        
        public int getTotalRows() {
            return totalRows;
        }
        
        public void setTotalRows(int totalRows) {
            this.totalRows = totalRows;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }
    }

    /**
     * 通过dbaccess命令行方式执行存储过程
     * 确保与原始Shell脚本的执行环境一致
     * 
     * @param debugFile 调试文件路径
     * @param traceFlag 跟踪标志
     * @param taskId 任务ID
     * @return 执行是否成功
     */
    private boolean executeStoredProcedureViaDbAccess(String debugFile, String traceFlag, String taskId) {
        // MDC上下文已在调用方法中设置，这里的日志会自动包含interfaceId信息
        try {
            // 获取数据库名称
            String databaseName = databaseUtil.getDatabaseName();
            log.info("通过dbaccess执行存储过程，数据库: {}", databaseName);
            
            // 构建存储过程调用SQL，与原始脚本保持一致
            String sqlCommand = String.format(
                "set lock mode to wait 10;call bmssp_VGOP_banalyse(\"%s\",\"%s\",\"%s000000\");",
                debugFile, 0, taskId
            );
            
            log.info("存储过程SQL命令: {}", sqlCommand);
            
            // 构建dbaccess命令
            ProcessBuilder processBuilder = new ProcessBuilder("dbaccess", databaseName);
            processBuilder.redirectErrorStream(true);
            
            Process process = processBuilder.start();
            
            // 向进程输入SQL命令
            try (var outputStream = process.getOutputStream()) {
                outputStream.write(sqlCommand.getBytes());
                outputStream.flush();
            }
            
            // 读取进程输出
            StringBuilder output = new StringBuilder();
            try (var inputStream = process.getInputStream();
                 var reader = new java.io.BufferedReader(new java.io.InputStreamReader(inputStream))) {
                
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }
            
            // 等待进程完成
            int exitCode = process.waitFor();
            String processOutput = output.toString();
            
            log.info("dbaccess存储过程执行结果 - 退出码: {}, 输出: {}", exitCode, processOutput);
            
            if (exitCode == 0) {
                log.info("存储过程通过dbaccess执行成功");
                return true;
            } else {
                log.error("存储过程通过dbaccess执行失败，退出码: {}, 输出: {}", exitCode, processOutput);
                return false;
            }
            
        } catch (Exception e) {
            log.error("通过dbaccess执行存储过程时发生异常", e);
            return false;
        }
    }
    
} 