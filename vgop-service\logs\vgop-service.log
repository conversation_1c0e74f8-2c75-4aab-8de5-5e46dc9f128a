2025-07-17 12:13:33.088 [background-preinit] INFO  o.h.validator.internal.util.Version [||||] [] - HV000001: Hibernate Validator 6.2.0.Final
2025-07-17 12:13:33.162 [restartedMain] INFO  c.v.service.VgopServiceApplication [||||] [] - Starting VgopServiceApplication using Java 1.8.0_452 on ziber with PID 25692 (C:\workspaces\hdh\vgop-vli\vgop-service\target\classes started by galil in C:\workspaces\hdh\vgop-vli\vgop-service)
2025-07-17 12:13:33.163 [restartedMain] DEBUG c.v.service.VgopServiceApplication [||||] [] - Running with Spring Boot v2.5.5, Spring v5.3.19
2025-07-17 12:13:33.164 [restartedMain] INFO  c.v.service.VgopServiceApplication [||||] [] - The following profiles are active: dev
2025-07-17 12:13:33.226 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor [||||] [] - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-17 12:13:33.226 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor [||||] [] - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-17 12:13:34.412 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\DataExportMapper.class]
2025-07-17 12:13:34.412 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\RevisionMapper.class]
2025-07-17 12:13:34.412 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\ValidationAlertsMapper.class]
2025-07-17 12:13:34.412 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\primary\RevisionTimesMapper.class]
2025-07-17 12:13:34.413 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\secondary\TaskExecutionMapper.class]
2025-07-17 12:13:34.414 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'dataExportMapper' and 'com.vgop.service.dao.DataExportMapper' mapperInterface
2025-07-17 12:13:34.418 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'revisionMapper' and 'com.vgop.service.dao.RevisionMapper' mapperInterface
2025-07-17 12:13:34.418 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'validationAlertsMapper' and 'com.vgop.service.dao.ValidationAlertsMapper' mapperInterface
2025-07-17 12:13:34.418 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'revisionTimesMapper' and 'com.vgop.service.dao.primary.RevisionTimesMapper' mapperInterface
2025-07-17 12:13:34.418 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'taskExecutionMapper' and 'com.vgop.service.dao.secondary.TaskExecutionMapper' mapperInterface
2025-07-17 12:13:34.638 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [||||] [] - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-17 12:13:35.330 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer [||||] [] - Tomcat initialized with port(s): 8080 (http)
2025-07-17 12:13:35.341 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol [||||] [] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-17 12:13:35.344 [restartedMain] INFO  o.a.catalina.core.StandardService [||||] [] - Starting service [Tomcat]
2025-07-17 12:13:35.344 [restartedMain] INFO  o.a.catalina.core.StandardEngine [||||] [] - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-17 12:13:35.482 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/vgop] [||||] [] - Initializing Spring embedded WebApplicationContext
2025-07-17 12:13:35.482 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext [||||] [] - Root WebApplicationContext: initialization completed in 2255 ms
2025-07-17 12:13:35.702 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - ValidationEngine已初始化，等待规则配置...
2025-07-17 12:13:36.526 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\mapper\DataExportMapper.xml]'
2025-07-17 12:13:36.543 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\mapper\RevisionMapper.xml]'
2025-07-17 12:13:36.553 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\mapper\TaskExecutionMapper.xml]'
2025-07-17 12:13:36.563 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\mapper\ValidationAlertsMapper.xml]'
2025-07-17 12:13:36.741 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - === 开始测试数据库连接 ===
2025-07-17 12:13:36.741 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 测试主数据源连接...
2025-07-17 12:13:36.777 [restartedMain] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - testWhileIdle is true, validationQuery not set
2025-07-17 12:13:36.794 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-1} inited
2025-07-17 12:13:57.874 [Druid-ConnectionPool-Create-236344993] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - create connection SQLException, url: jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;, errorCode -908, state 08004
java.sql.SQLException: com.gbasedbt.asf.IfxASFException: Attempt to connect to database server (gb33207) failed.
	at com.gbasedbt.jdbc.IfxSqliConnect.<init>(IfxSqliConnect.java:1569)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.gbasedbt.jdbc.Driver.connect(Driver.java:245)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1657)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1723)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2838)
Caused by: com.gbasedbt.asf.IfxASFException: Attempt to connect to database server (gb33207) failed.
	at com.gbasedbt.util.IfxErrMsg.getLocIfxASFException(IfxErrMsg.java:751)
	at com.gbasedbt.asf.Connection.openSocket(Connection.java:2050)
	at com.gbasedbt.asf.Connection.<init>(Connection.java:529)
	at com.gbasedbt.jdbc.IfxSqliConnect.<init>(IfxSqliConnect.java:1289)
	... 8 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at java.net.Socket.connect(Socket.java:556)
	at com.gbasedbt.asf.Connection.getSocket(Connection.java:2397)
	at com.gbasedbt.asf.Connection.openSocket(Connection.java:1998)
	... 10 common frames omitted
2025-07-17 12:14:18.940 [Druid-ConnectionPool-Create-236344993] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - create connection SQLException, url: jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;, errorCode -908, state 08004
java.sql.SQLException: com.gbasedbt.asf.IfxASFException: Attempt to connect to database server (gb33207) failed.
	at com.gbasedbt.jdbc.IfxSqliConnect.<init>(IfxSqliConnect.java:1569)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.gbasedbt.jdbc.Driver.connect(Driver.java:245)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1657)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1723)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2838)
Caused by: com.gbasedbt.asf.IfxASFException: Attempt to connect to database server (gb33207) failed.
	at com.gbasedbt.util.IfxErrMsg.getLocIfxASFException(IfxErrMsg.java:751)
	at com.gbasedbt.asf.Connection.openSocket(Connection.java:2050)
	at com.gbasedbt.asf.Connection.<init>(Connection.java:529)
	at com.gbasedbt.jdbc.IfxSqliConnect.<init>(IfxSqliConnect.java:1289)
	... 8 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at java.net.Socket.connect(Socket.java:556)
	at com.gbasedbt.asf.Connection.getSocket(Connection.java:2397)
	at com.gbasedbt.asf.Connection.openSocket(Connection.java:1998)
	... 10 common frames omitted
2025-07-17 12:14:18.941 [Druid-ConnectionPool-Create-236344993] INFO  c.a.d.pool.DruidAbstractDataSource [||||] [] - {dataSource-1} failContinuous is true
2025-07-17 12:14:40.500 [Druid-ConnectionPool-Create-236344993] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - create connection SQLException, url: jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;, errorCode -908, state 08004
java.sql.SQLException: com.gbasedbt.asf.IfxASFException: Attempt to connect to database server (gb33207) failed.
	at com.gbasedbt.jdbc.IfxSqliConnect.<init>(IfxSqliConnect.java:1569)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.gbasedbt.jdbc.Driver.connect(Driver.java:245)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1657)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1723)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2838)
Caused by: com.gbasedbt.asf.IfxASFException: Attempt to connect to database server (gb33207) failed.
	at com.gbasedbt.util.IfxErrMsg.getLocIfxASFException(IfxErrMsg.java:751)
	at com.gbasedbt.asf.Connection.openSocket(Connection.java:2050)
	at com.gbasedbt.asf.Connection.<init>(Connection.java:529)
	at com.gbasedbt.jdbc.IfxSqliConnect.<init>(IfxSqliConnect.java:1289)
	... 8 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at java.net.Socket.connect(Socket.java:556)
	at com.gbasedbt.asf.Connection.getSocket(Connection.java:2397)
	at com.gbasedbt.asf.Connection.openSocket(Connection.java:1998)
	... 10 common frames omitted
2025-07-17 12:15:02.036 [Druid-ConnectionPool-Create-236344993] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - create connection SQLException, url: jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;, errorCode -908, state 08004
java.sql.SQLException: com.gbasedbt.asf.IfxASFException: Attempt to connect to database server (gb33207) failed.
	at com.gbasedbt.jdbc.IfxSqliConnect.<init>(IfxSqliConnect.java:1569)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.gbasedbt.jdbc.Driver.connect(Driver.java:245)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1657)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1723)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2838)
Caused by: com.gbasedbt.asf.IfxASFException: Attempt to connect to database server (gb33207) failed.
	at com.gbasedbt.util.IfxErrMsg.getLocIfxASFException(IfxErrMsg.java:751)
	at com.gbasedbt.asf.Connection.openSocket(Connection.java:2050)
	at com.gbasedbt.asf.Connection.<init>(Connection.java:529)
	at com.gbasedbt.jdbc.IfxSqliConnect.<init>(IfxSqliConnect.java:1289)
	... 8 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at java.net.Socket.connect(Socket.java:556)
	at com.gbasedbt.asf.Connection.getSocket(Connection.java:2397)
	at com.gbasedbt.asf.Connection.openSocket(Connection.java:1998)
	... 10 common frames omitted
2025-07-17 12:15:23.587 [Druid-ConnectionPool-Create-236344993] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - create connection SQLException, url: jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;, errorCode -908, state 08004
java.sql.SQLException: com.gbasedbt.asf.IfxASFException: Attempt to connect to database server (gb33207) failed.
	at com.gbasedbt.jdbc.IfxSqliConnect.<init>(IfxSqliConnect.java:1569)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.gbasedbt.jdbc.Driver.connect(Driver.java:245)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1657)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1723)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2838)
Caused by: com.gbasedbt.asf.IfxASFException: Attempt to connect to database server (gb33207) failed.
	at com.gbasedbt.util.IfxErrMsg.getLocIfxASFException(IfxErrMsg.java:751)
	at com.gbasedbt.asf.Connection.openSocket(Connection.java:2050)
	at com.gbasedbt.asf.Connection.<init>(Connection.java:529)
	at com.gbasedbt.jdbc.IfxSqliConnect.<init>(IfxSqliConnect.java:1289)
	... 8 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at java.net.Socket.connect(Socket.java:556)
	at com.gbasedbt.asf.Connection.getSocket(Connection.java:2397)
	at com.gbasedbt.asf.Connection.openSocket(Connection.java:1998)
	... 10 common frames omitted
2025-07-17 12:15:45.133 [Druid-ConnectionPool-Create-236344993] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - create connection SQLException, url: jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;, errorCode -908, state 08004
java.sql.SQLException: com.gbasedbt.asf.IfxASFException: Attempt to connect to database server (gb33207) failed.
	at com.gbasedbt.jdbc.IfxSqliConnect.<init>(IfxSqliConnect.java:1569)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.gbasedbt.jdbc.Driver.connect(Driver.java:245)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1657)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1723)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2838)
Caused by: com.gbasedbt.asf.IfxASFException: Attempt to connect to database server (gb33207) failed.
	at com.gbasedbt.util.IfxErrMsg.getLocIfxASFException(IfxErrMsg.java:751)
	at com.gbasedbt.asf.Connection.openSocket(Connection.java:2050)
	at com.gbasedbt.asf.Connection.<init>(Connection.java:529)
	at com.gbasedbt.jdbc.IfxSqliConnect.<init>(IfxSqliConnect.java:1289)
	... 8 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at java.net.Socket.connect(Socket.java:556)
	at com.gbasedbt.asf.Connection.getSocket(Connection.java:2397)
	at com.gbasedbt.asf.Connection.openSocket(Connection.java:1998)
	... 10 common frames omitted
2025-07-17 12:16:06.678 [Druid-ConnectionPool-Create-236344993] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - create connection SQLException, url: jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;, errorCode -908, state 08004
java.sql.SQLException: com.gbasedbt.asf.IfxASFException: Attempt to connect to database server (gb33207) failed.
	at com.gbasedbt.jdbc.IfxSqliConnect.<init>(IfxSqliConnect.java:1569)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.gbasedbt.jdbc.Driver.connect(Driver.java:245)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1657)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1723)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2838)
Caused by: com.gbasedbt.asf.IfxASFException: Attempt to connect to database server (gb33207) failed.
	at com.gbasedbt.util.IfxErrMsg.getLocIfxASFException(IfxErrMsg.java:751)
	at com.gbasedbt.asf.Connection.openSocket(Connection.java:2050)
	at com.gbasedbt.asf.Connection.<init>(Connection.java:529)
	at com.gbasedbt.jdbc.IfxSqliConnect.<init>(IfxSqliConnect.java:1289)
	... 8 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at java.net.Socket.connect(Socket.java:556)
	at com.gbasedbt.asf.Connection.getSocket(Connection.java:2397)
	at com.gbasedbt.asf.Connection.openSocket(Connection.java:1998)
	... 10 common frames omitted
2025-07-17 12:16:28.236 [Druid-ConnectionPool-Create-236344993] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - create connection SQLException, url: jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;, errorCode -908, state 08004
java.sql.SQLException: com.gbasedbt.asf.IfxASFException: Attempt to connect to database server (gb33207) failed.
	at com.gbasedbt.jdbc.IfxSqliConnect.<init>(IfxSqliConnect.java:1569)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.gbasedbt.jdbc.Driver.connect(Driver.java:245)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1657)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1723)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2838)
Caused by: com.gbasedbt.asf.IfxASFException: Attempt to connect to database server (gb33207) failed.
	at com.gbasedbt.util.IfxErrMsg.getLocIfxASFException(IfxErrMsg.java:751)
	at com.gbasedbt.asf.Connection.openSocket(Connection.java:2050)
	at com.gbasedbt.asf.Connection.<init>(Connection.java:529)
	at com.gbasedbt.jdbc.IfxSqliConnect.<init>(IfxSqliConnect.java:1289)
	... 8 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at java.net.Socket.connect(Socket.java:556)
	at com.gbasedbt.asf.Connection.getSocket(Connection.java:2397)
	at com.gbasedbt.asf.Connection.openSocket(Connection.java:1998)
	... 10 common frames omitted
2025-07-17 12:16:49.782 [Druid-ConnectionPool-Create-236344993] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - create connection SQLException, url: jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;, errorCode -908, state 08004
java.sql.SQLException: com.gbasedbt.asf.IfxASFException: Attempt to connect to database server (gb33207) failed.
	at com.gbasedbt.jdbc.IfxSqliConnect.<init>(IfxSqliConnect.java:1569)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.gbasedbt.jdbc.Driver.connect(Driver.java:245)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1657)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1723)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2838)
Caused by: com.gbasedbt.asf.IfxASFException: Attempt to connect to database server (gb33207) failed.
	at com.gbasedbt.util.IfxErrMsg.getLocIfxASFException(IfxErrMsg.java:751)
	at com.gbasedbt.asf.Connection.openSocket(Connection.java:2050)
	at com.gbasedbt.asf.Connection.<init>(Connection.java:529)
	at com.gbasedbt.jdbc.IfxSqliConnect.<init>(IfxSqliConnect.java:1289)
	... 8 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at java.net.Socket.connect(Socket.java:556)
	at com.gbasedbt.asf.Connection.getSocket(Connection.java:2397)
	at com.gbasedbt.asf.Connection.openSocket(Connection.java:1998)
	... 10 common frames omitted
2025-07-17 12:17:11.326 [Druid-ConnectionPool-Create-236344993] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - create connection SQLException, url: jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;, errorCode -908, state 08004
java.sql.SQLException: com.gbasedbt.asf.IfxASFException: Attempt to connect to database server (gb33207) failed.
	at com.gbasedbt.jdbc.IfxSqliConnect.<init>(IfxSqliConnect.java:1569)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.gbasedbt.jdbc.Driver.connect(Driver.java:245)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1657)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1723)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2838)
Caused by: com.gbasedbt.asf.IfxASFException: Attempt to connect to database server (gb33207) failed.
	at com.gbasedbt.util.IfxErrMsg.getLocIfxASFException(IfxErrMsg.java:751)
	at com.gbasedbt.asf.Connection.openSocket(Connection.java:2050)
	at com.gbasedbt.asf.Connection.<init>(Connection.java:529)
	at com.gbasedbt.jdbc.IfxSqliConnect.<init>(IfxSqliConnect.java:1289)
	... 8 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at java.net.Socket.connect(Socket.java:556)
	at com.gbasedbt.asf.Connection.getSocket(Connection.java:2397)
	at com.gbasedbt.asf.Connection.openSocket(Connection.java:1998)
	... 10 common frames omitted
2025-07-17 12:17:32.901 [Druid-ConnectionPool-Create-236344993] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - create connection SQLException, url: jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;, errorCode -908, state 08004
java.sql.SQLException: com.gbasedbt.asf.IfxASFException: Attempt to connect to database server (gb33207) failed.
	at com.gbasedbt.jdbc.IfxSqliConnect.<init>(IfxSqliConnect.java:1569)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.gbasedbt.jdbc.Driver.connect(Driver.java:245)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1657)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1723)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2838)
Caused by: com.gbasedbt.asf.IfxASFException: Attempt to connect to database server (gb33207) failed.
	at com.gbasedbt.util.IfxErrMsg.getLocIfxASFException(IfxErrMsg.java:751)
	at com.gbasedbt.asf.Connection.openSocket(Connection.java:2050)
	at com.gbasedbt.asf.Connection.<init>(Connection.java:529)
	at com.gbasedbt.jdbc.IfxSqliConnect.<init>(IfxSqliConnect.java:1289)
	... 8 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at java.net.Socket.connect(Socket.java:556)
	at com.gbasedbt.asf.Connection.getSocket(Connection.java:2397)
	at com.gbasedbt.asf.Connection.openSocket(Connection.java:1998)
	... 10 common frames omitted
2025-07-17 12:17:54.462 [Druid-ConnectionPool-Create-236344993] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - create connection SQLException, url: jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;, errorCode -908, state 08004
java.sql.SQLException: com.gbasedbt.asf.IfxASFException: Attempt to connect to database server (gb33207) failed.
	at com.gbasedbt.jdbc.IfxSqliConnect.<init>(IfxSqliConnect.java:1569)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.gbasedbt.jdbc.Driver.connect(Driver.java:245)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1657)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1723)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2838)
Caused by: com.gbasedbt.asf.IfxASFException: Attempt to connect to database server (gb33207) failed.
	at com.gbasedbt.util.IfxErrMsg.getLocIfxASFException(IfxErrMsg.java:751)
	at com.gbasedbt.asf.Connection.openSocket(Connection.java:2050)
	at com.gbasedbt.asf.Connection.<init>(Connection.java:529)
	at com.gbasedbt.jdbc.IfxSqliConnect.<init>(IfxSqliConnect.java:1289)
	... 8 common frames omitted
Caused by: java.net.ConnectException: Connection timed out: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at java.net.Socket.connect(Socket.java:556)
	at com.gbasedbt.asf.Connection.getSocket(Connection.java:2397)
	at com.gbasedbt.asf.Connection.openSocket(Connection.java:1998)
	... 10 common frames omitted
2025-07-17 12:17:55.296 [Druid-ConnectionPool-Create-236344993] INFO  c.a.d.pool.DruidAbstractDataSource [||||] [] - {dataSource-1} failContinuous is false
2025-07-17 12:17:55.648 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 主数据源连接成功: {databaseProductVersion=12.10.FC4G1AEE, databaseProductName=GBase Server, driverVersion=4.10.JC4G1N999, name=主数据源, driverName=GBase JDBC Driver for GBase Server, userName=ismp, url=jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;}
2025-07-17 12:17:55.649 [restartedMain] INFO  com.vgop.service.util.DatabaseUtil [||||] [] - 检测到数据库产品: GBase Server, URL: jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;
2025-07-17 12:17:55.649 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 检测到主数据库类型: gbase
2025-07-17 12:17:55.649 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 是否支持UNLOAD命令: true
2025-07-17 12:17:55.649 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 测试次数据源连接...
2025-07-17 12:17:55.650 [restartedMain] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - testWhileIdle is true, validationQuery not set
2025-07-17 12:17:55.652 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-2} inited
2025-07-17 12:17:56.478 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 次数据源连接成功: {databaseProductVersion=12.10.FC4G1AEE, databaseProductName=GBase Server, driverVersion=4.10.JC4G1N999, name=次数据源, driverName=GBase JDBC Driver for GBase Server, userName=ismp, url=jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;}
2025-07-17 12:17:56.479 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - === 数据库连接测试完成 ===
2025-07-17 12:17:56.890 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory [||||] [] - Using default implementation for ThreadExecutor
2025-07-17 12:17:56.912 [restartedMain] INFO  o.quartz.core.SchedulerSignalerImpl [||||] [] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-17 12:17:56.912 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - Quartz Scheduler v.2.3.2 created.
2025-07-17 12:17:56.916 [restartedMain] INFO  org.quartz.simpl.RAMJobStore [||||] [] - RAMJobStore initialized.
2025-07-17 12:17:56.917 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-17 12:17:56.917 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory [||||] [] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-17 12:17:56.917 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory [||||] [] - Quartz scheduler version: 2.3.2
2025-07-17 12:17:56.918 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@77654ae6
2025-07-17 12:17:56.986 [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver [||||] [] - Exposing 15 endpoint(s) beneath base path '/actuator'
2025-07-17 12:17:57.099 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:17:57.105 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库产品名称: GBase Server, 版本: 12.10.FC4G1AEE
2025-07-17 12:17:57.106 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 检测到数据库类型: GBase Server
2025-07-17 12:17:57.106 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 配置GBase数据库字符编码设置
2025-07-17 12:17:57.108 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]
2025-07-17 12:17:57.110 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:17:57.226 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Looking up default SQLErrorCodes for DataSource [com.alibaba.druid.pool.DruidDataSource@e1656a1]
2025-07-17 12:17:57.227 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:17:57.228 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - SQL error codes for 'GBase Server' not found
2025-07-17 12:17:57.228 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Caching SQL error codes for DataSource [com.alibaba.druid.pool.DruidDataSource@e1656a1]: database product name is 'GBase Server'
2025-07-17 12:17:57.229 [restartedMain] DEBUG o.s.j.s.SQLErrorCodeSQLExceptionTranslator [||||] [] - Unable to translate SQLException with Error code '-728', will now try the fallback translator
2025-07-17 12:17:57.229 [restartedMain] DEBUG o.s.j.s.SQLStateSQLExceptionTranslator [||||] [] - Extracted SQL state class 'IX' from value 'IX000'
2025-07-17 12:17:57.231 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 查询GBase数据库字符编码信息失败: StatementCallback; uncategorized SQLException for SQL [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]; SQL state [IX000]; error code [-728]; Unknown first argument of dbinfo(dblocale).; nested exception is java.sql.SQLException: Unknown first argument of dbinfo(dblocale).
2025-07-17 12:17:57.231 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - GBase数据库字符编码配置完成，依赖JDBC URL参数: DB_LOCALE, NEWCODESET
2025-07-17 12:17:57.232 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dbhostname') FROM systables WHERE tabid = 1]
2025-07-17 12:17:57.232 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:17:57.525 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库主机名: eb33207
2025-07-17 12:17:57.526 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT LENGTH('测试中文字符') FROM systables WHERE tabid = 1]
2025-07-17 12:17:57.526 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:17:57.809 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 字符编码兼容性测试通过，中文字符长度: 12
2025-07-17 12:17:57.809 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 已配置主数据源字符编码安全的JdbcTemplate
2025-07-17 12:17:57.811 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:17:57.811 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库产品名称: GBase Server, 版本: 12.10.FC4G1AEE
2025-07-17 12:17:57.811 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 检测到数据库类型: GBase Server
2025-07-17 12:17:57.811 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 配置GBase数据库字符编码设置
2025-07-17 12:17:57.811 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]
2025-07-17 12:17:57.812 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:17:57.868 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Looking up default SQLErrorCodes for DataSource [com.alibaba.druid.pool.DruidDataSource@5dfb24bc]
2025-07-17 12:17:57.869 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:17:57.869 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - SQL error codes for 'GBase Server' not found
2025-07-17 12:17:57.869 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Caching SQL error codes for DataSource [com.alibaba.druid.pool.DruidDataSource@5dfb24bc]: database product name is 'GBase Server'
2025-07-17 12:17:57.869 [restartedMain] DEBUG o.s.j.s.SQLErrorCodeSQLExceptionTranslator [||||] [] - Unable to translate SQLException with Error code '-728', will now try the fallback translator
2025-07-17 12:17:57.869 [restartedMain] DEBUG o.s.j.s.SQLStateSQLExceptionTranslator [||||] [] - Extracted SQL state class 'IX' from value 'IX000'
2025-07-17 12:17:57.869 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 查询GBase数据库字符编码信息失败: StatementCallback; uncategorized SQLException for SQL [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]; SQL state [IX000]; error code [-728]; Unknown first argument of dbinfo(dblocale).; nested exception is java.sql.SQLException: Unknown first argument of dbinfo(dblocale).
2025-07-17 12:17:57.869 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - GBase数据库字符编码配置完成，依赖JDBC URL参数: DB_LOCALE, NEWCODESET
2025-07-17 12:17:57.869 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dbhostname') FROM systables WHERE tabid = 1]
2025-07-17 12:17:57.869 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:17:58.157 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库主机名: eb33207
2025-07-17 12:17:58.157 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT LENGTH('测试中文字符') FROM systables WHERE tabid = 1]
2025-07-17 12:17:58.157 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:17:58.439 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 字符编码兼容性测试通过，中文字符长度: 12
2025-07-17 12:17:58.439 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 已配置次数据源字符编码安全的JdbcTemplate
2025-07-17 12:17:58.545 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer [||||] [] - LiveReload server is running on port 35729
2025-07-17 12:17:58.913 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol [||||] [] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-17 12:17:58.957 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer [||||] [] - Tomcat started on port(s): 8080 (http) with context path '/vgop'
2025-07-17 12:17:59.459 [restartedMain] INFO  o.s.s.quartz.SchedulerFactoryBean [||||] [] - Starting Quartz Scheduler now
2025-07-17 12:17:59.459 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-17 12:17:59.480 [restartedMain] INFO  c.v.service.VgopServiceApplication [||||] [] - Started VgopServiceApplication in 267.297 seconds (JVM running for 269.179)
2025-07-17 12:17:59.490 [restartedMain] INFO  c.v.s.s.ConfigValidationService [||||] [] - 开始验证应用配置...
2025-07-17 12:17:59.490 [restartedMain] INFO  c.v.s.s.ConfigValidationService [||||] [] - 配置验证通过
2025-07-17 12:17:59.490 [restartedMain] INFO  c.v.s.service.DirectoryInitService [||||] [] - 开始初始化目录结构...
2025-07-17 12:17:59.491 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 数据导出根目录 - ./VGOPdata/datafile/
2025-07-17 12:17:59.491 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日志根目录 - ./logs/
2025-07-17 12:17:59.491 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 备份根目录 - ./data/backup/
2025-07-17 12:17:59.491 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 告警文件目录 - ./data/alerts/
2025-07-17 12:17:59.492 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250714 - ./VGOPdata/datafile//20250714/day
2025-07-17 12:17:59.492 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250714 - ./VGOPdata/datafile//20250714/month
2025-07-17 12:17:59.492 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250715 - ./VGOPdata/datafile//20250715/day
2025-07-17 12:17:59.493 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250715 - ./VGOPdata/datafile//20250715/month
2025-07-17 12:17:59.493 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250716 - ./VGOPdata/datafile//20250716/day
2025-07-17 12:17:59.493 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250716 - ./VGOPdata/datafile//20250716/month
2025-07-17 12:17:59.494 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250717 - ./VGOPdata/datafile//20250717/day
2025-07-17 12:17:59.494 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250717 - ./VGOPdata/datafile//20250717/month
2025-07-17 12:17:59.494 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250718 - ./VGOPdata/datafile//20250718/day
2025-07-17 12:17:59.494 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250718 - ./VGOPdata/datafile//20250718/month
2025-07-17 12:17:59.496 [restartedMain] INFO  c.v.s.service.DirectoryInitService [||||] [] - 创建目录成功: 日数据目录 - 20250719 - ./VGOPdata/datafile//20250719/day
2025-07-17 12:17:59.498 [restartedMain] INFO  c.v.s.service.DirectoryInitService [||||] [] - 创建目录成功: 月数据目录 - 20250719 - ./VGOPdata/datafile//20250719/month
2025-07-17 12:17:59.498 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月度数据目录 - 202507 - ./VGOPdata/datafile//202507
2025-07-17 12:17:59.498 [restartedMain] INFO  c.v.s.service.DirectoryInitService [||||] [] - 目录结构初始化完成
2025-07-17 12:17:59.500 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 开始从配置文件加载校验规则...
2025-07-17 12:17:59.500 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24201 的校验规则...
2025-07-17 12:17:59.500 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phonenumber.format - FORMAT
2025-07-17 12:17:59.501 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-17 12:17:59.501 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phonenumber 添加了规则: FORMAT
2025-07-17 12:17:59.502 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phonenumber.length - LENGTH
2025-07-17 12:17:59.502 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-17 12:17:59.502 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phonenumber 添加了规则: LENGTH
2025-07-17 12:17:59.502 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phonestate.enum - ENUM
2025-07-17 12:17:59.502 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phonestate, 规则ID=common.phonestate.enum, 规则名称=枚举值校验
2025-07-17 12:17:59.502 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phonestate 添加了规则: ENUM
2025-07-17 12:17:59.502 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phoneimsi.length - LENGTH
2025-07-17 12:17:59.503 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phoneimsi, 规则ID=common.phoneimsi.length, 规则名称=长度校验
2025-07-17 12:17:59.503 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phoneimsi 添加了规则: LENGTH
2025-07-17 12:17:59.503 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phoneimei.length - LENGTH
2025-07-17 12:17:59.503 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phoneimei, 规则ID=common.phoneimei.length, 规则名称=长度校验
2025-07-17 12:17:59.503 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phoneimei 添加了规则: LENGTH
2025-07-17 12:17:59.503 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.locationid.length - LENGTH
2025-07-17 12:17:59.503 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=locationid, 规则ID=common.locationid.length, 规则名称=长度校验
2025-07-17 12:17:59.504 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 locationid 添加了规则: LENGTH
2025-07-17 12:17:59.504 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.provinceid.length - LENGTH
2025-07-17 12:17:59.504 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=provinceid, 规则ID=common.provinceid.length, 规则名称=长度校验
2025-07-17 12:17:59.504 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 provinceid 添加了规则: LENGTH
2025-07-17 12:17:59.504 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.openingtime.format - FORMAT
2025-07-17 12:17:59.504 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=openingtime, 规则ID=common.openingtime.format, 规则名称=格式校验
2025-07-17 12:17:59.504 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 openingtime 添加了规则: FORMAT
2025-07-17 12:17:59.504 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.optime.format - FORMAT
2025-07-17 12:17:59.504 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=Optime, 规则ID=common.Optime.format, 规则名称=格式校验
2025-07-17 12:17:59.504 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 Optime 添加了规则: FORMAT
2025-07-17 12:17:59.504 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.sex.enum - ENUM
2025-07-17 12:17:59.504 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=sex, 规则ID=common.sex.enum, 规则名称=枚举值校验
2025-07-17 12:17:59.504 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 sex 添加了规则: ENUM
2025-07-17 12:17:59.504 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24201, 字段数量=9
2025-07-17 12:17:59.504 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 注册了 9 个字段的元数据
2025-07-17 12:17:59.504 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [phonestate, Optime, phoneimei, openingtime, locationid, sex, phonenumber, phoneimsi, provinceid] -> VGOP1-R2-10-24201
2025-07-17 12:17:59.505 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 注册了字段到接口的映射关系
2025-07-17 12:17:59.505 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24201 加载了 10 个字段校验规则
2025-07-17 12:17:59.505 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24202 的校验规则...
2025-07-17 12:17:59.505 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnumber.format - FORMAT
2025-07-17 12:17:59.505 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-17 12:17:59.505 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnumber 添加了规则: FORMAT
2025-07-17 12:17:59.505 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnumber.length - LENGTH
2025-07-17 12:17:59.505 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnumber, 规则ID=common.mcnnumber.length, 规则名称=长度校验
2025-07-17 12:17:59.505 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnumber 添加了规则: LENGTH
2025-07-17 12:17:59.505 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.phonenumber.format - FORMAT
2025-07-17 12:17:59.505 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-17 12:17:59.507 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 phonenumber 添加了规则: FORMAT
2025-07-17 12:17:59.507 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.phonenumber.length - LENGTH
2025-07-17 12:17:59.507 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-17 12:17:59.507 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 phonenumber 添加了规则: LENGTH
2025-07-17 12:17:59.507 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.business.enum - ENUM
2025-07-17 12:17:59.507 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=business, 规则ID=common.business.enum, 规则名称=枚举值校验
2025-07-17 12:17:59.507 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 business 添加了规则: ENUM
2025-07-17 12:17:59.507 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.shutdown.length - LENGTH
2025-07-17 12:17:59.507 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=shutdown, 规则ID=common.shutdown.length, 规则名称=长度校验
2025-07-17 12:17:59.507 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 shutdown 添加了规则: LENGTH
2025-07-17 12:17:59.507 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnimsi.length - LENGTH
2025-07-17 12:17:59.507 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnimsi, 规则ID=common.mcnimsi.length, 规则名称=长度校验
2025-07-17 12:17:59.507 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnimsi 添加了规则: LENGTH
2025-07-17 12:17:59.508 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnimsi.format - FORMAT
2025-07-17 12:17:59.508 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnimsi, 规则ID=common.mcnimsi.format, 规则名称=格式校验
2025-07-17 12:17:59.508 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnimsi 添加了规则: FORMAT
2025-07-17 12:17:59.508 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnlocationid.length - LENGTH
2025-07-17 12:17:59.508 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnlocationid, 规则ID=common.mcnlocationid.length, 规则名称=长度校验
2025-07-17 12:17:59.508 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnlocationid 添加了规则: LENGTH
2025-07-17 12:17:59.508 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.numstate.length - LENGTH
2025-07-17 12:17:59.508 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=numstate, 规则ID=common.numstate.length, 规则名称=长度校验
2025-07-17 12:17:59.508 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 numstate 添加了规则: LENGTH
2025-07-17 12:17:59.508 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnature.enum - ENUM
2025-07-17 12:17:59.508 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnature, 规则ID=common.mcnnature.enum, 规则名称=枚举值校验
2025-07-17 12:17:59.508 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnature 添加了规则: ENUM
2025-07-17 12:17:59.508 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnum.enum - ENUM
2025-07-17 12:17:59.509 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnum, 规则ID=common.mcnnum.enum, 规则名称=枚举值校验
2025-07-17 12:17:59.509 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnum 添加了规则: ENUM
2025-07-17 12:17:59.509 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnum.length - LENGTH
2025-07-17 12:17:59.509 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnum, 规则ID=common.mcnnum.length, 规则名称=长度校验
2025-07-17 12:17:59.509 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnum 添加了规则: LENGTH
2025-07-17 12:17:59.509 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.channel.length - LENGTH
2025-07-17 12:17:59.509 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=channel, 规则ID=common.channel.length, 规则名称=长度校验
2025-07-17 12:17:59.509 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 channel 添加了规则: LENGTH
2025-07-17 12:17:59.509 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mj.enum - ENUM
2025-07-17 12:17:59.509 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mj, 规则ID=common.mj.enum, 规则名称=枚举值校验
2025-07-17 12:17:59.509 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mj 添加了规则: ENUM
2025-07-17 12:17:59.509 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.openingtime.format - FORMAT
2025-07-17 12:17:59.509 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=openingtime, 规则ID=common.openingtime.format, 规则名称=格式校验
2025-07-17 12:17:59.510 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 openingtime 添加了规则: FORMAT
2025-07-17 12:17:59.510 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.optime.format - FORMAT
2025-07-17 12:17:59.510 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=Optime, 规则ID=common.Optime.format, 规则名称=格式校验
2025-07-17 12:17:59.510 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 Optime 添加了规则: FORMAT
2025-07-17 12:17:59.510 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcimsitime.format - FORMAT
2025-07-17 12:17:59.510 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcimsitime, 规则ID=common.mcimsitime.format, 规则名称=格式校验
2025-07-17 12:17:59.510 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcimsitime 添加了规则: FORMAT
2025-07-17 12:17:59.510 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.usertype.length - LENGTH
2025-07-17 12:17:59.510 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=usertype, 规则ID=common.usertype.length, 规则名称=长度校验
2025-07-17 12:17:59.510 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 usertype 添加了规则: LENGTH
2025-07-17 12:17:59.510 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.begintime.format - FORMAT
2025-07-17 12:17:59.510 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=Begintime, 规则ID=common.Begintime.format, 规则名称=格式校验
2025-07-17 12:17:59.510 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 Begintime 添加了规则: FORMAT
2025-07-17 12:17:59.510 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.endtime.format - FORMAT
2025-07-17 12:17:59.510 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=Endtime, 规则ID=common.Endtime.format, 规则名称=格式校验
2025-07-17 12:17:59.510 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 Endtime 添加了规则: FORMAT
2025-07-17 12:17:59.510 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.servid.length - LENGTH
2025-07-17 12:17:59.510 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=ServID, 规则ID=common.ServID.length, 规则名称=长度校验
2025-07-17 12:17:59.510 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 ServID 添加了规则: LENGTH
2025-07-17 12:17:59.510 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24202, 字段数量=18
2025-07-17 12:17:59.510 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 注册了 18 个字段的元数据
2025-07-17 12:17:59.510 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [mcnlocationid, Optime, business, mcnimsi, mcimsitime, phonenumber, mcnnum, channel, usertype, numstate, Endtime, openingtime, ServID, mcnnature, Begintime, mcnnumber, mj, shutdown] -> VGOP1-R2-10-24202
2025-07-17 12:17:59.511 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 注册了字段到接口的映射关系
2025-07-17 12:17:59.511 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24202 加载了 22 个字段校验规则
2025-07-17 12:17:59.511 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24203 的校验规则...
2025-07-17 12:17:59.511 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.account_id.length - LENGTH
2025-07-17 12:17:59.511 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=account_id, 规则ID=common.account_id.length, 规则名称=长度校验
2025-07-17 12:17:59.511 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 account_id 添加了规则: LENGTH
2025-07-17 12:17:59.511 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.phonenumber.format - FORMAT
2025-07-17 12:17:59.511 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-17 12:17:59.512 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 phonenumber 添加了规则: FORMAT
2025-07-17 12:17:59.512 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.phonenumber.length - LENGTH
2025-07-17 12:17:59.512 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-17 12:17:59.512 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 phonenumber 添加了规则: LENGTH
2025-07-17 12:17:59.512 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.type.enum - ENUM
2025-07-17 12:17:59.512 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=type, 规则ID=common.type.enum, 规则名称=枚举值校验
2025-07-17 12:17:59.512 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 type 添加了规则: ENUM
2025-07-17 12:17:59.512 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.optime.format - FORMAT
2025-07-17 12:17:59.512 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=optime, 规则ID=common.optime.format, 规则名称=格式校验
2025-07-17 12:17:59.512 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 optime 添加了规则: FORMAT
2025-07-17 12:17:59.512 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.version.length - LENGTH
2025-07-17 12:17:59.512 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=version, 规则ID=common.version.length, 规则名称=长度校验
2025-07-17 12:17:59.512 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 version 添加了规则: LENGTH
2025-07-17 12:17:59.513 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24203, 字段数量=5
2025-07-17 12:17:59.513 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 注册了 5 个字段的元数据
2025-07-17 12:17:59.513 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [account_id, optime, phonenumber, type, version] -> VGOP1-R2-10-24203
2025-07-17 12:17:59.513 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 注册了字段到接口的映射关系
2025-07-17 12:17:59.513 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24203 加载了 6 个字段校验规则
2025-07-17 12:17:59.513 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24205 的校验规则...
2025-07-17 12:17:59.513 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.calltype.enum - ENUM
2025-07-17 12:17:59.514 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=callType, 规则ID=common.callType.enum, 规则名称=枚举值校验
2025-07-17 12:17:59.514 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 callType 添加了规则: ENUM
2025-07-17 12:17:59.514 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callingpartynumber.format - FORMAT
2025-07-17 12:17:59.514 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=callingPartyNumber, 规则ID=common.callingPartyNumber.format, 规则名称=格式校验
2025-07-17 12:17:59.514 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 callingPartyNumber 添加了规则: FORMAT
2025-07-17 12:17:59.514 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callingpartynumber.length - LENGTH
2025-07-17 12:17:59.514 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=callingPartyNumber, 规则ID=common.callingPartyNumber.length, 规则名称=长度校验
2025-07-17 12:17:59.514 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 callingPartyNumber 添加了规则: LENGTH
2025-07-17 12:17:59.514 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.calledpartynumber.format - FORMAT
2025-07-17 12:17:59.514 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=calledPartyNumber, 规则ID=common.calledPartyNumber.format, 规则名称=格式校验
2025-07-17 12:17:59.514 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 calledPartyNumber 添加了规则: FORMAT
2025-07-17 12:17:59.514 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.calledpartynumber.length - LENGTH
2025-07-17 12:17:59.514 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=calledPartyNumber, 规则ID=common.calledPartyNumber.length, 规则名称=长度校验
2025-07-17 12:17:59.514 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 calledPartyNumber 添加了规则: LENGTH
2025-07-17 12:17:59.514 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.mcnnumber.format - FORMAT
2025-07-17 12:17:59.514 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-17 12:17:59.514 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 mcnnumber 添加了规则: FORMAT
2025-07-17 12:17:59.515 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callbegintime.format - FORMAT
2025-07-17 12:17:59.515 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallBeginTime, 规则ID=common.CallBeginTime.format, 规则名称=格式校验
2025-07-17 12:17:59.515 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallBeginTime 添加了规则: FORMAT
2025-07-17 12:17:59.515 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callendtime.format - FORMAT
2025-07-17 12:17:59.515 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallEndTime, 规则ID=common.CallEndTime.format, 规则名称=格式校验
2025-07-17 12:17:59.515 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallEndTime 添加了规则: FORMAT
2025-07-17 12:17:59.515 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callduration.format - FORMAT
2025-07-17 12:17:59.515 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallDuration, 规则ID=common.CallDuration.format, 规则名称=格式校验
2025-07-17 12:17:59.515 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallDuration 添加了规则: FORMAT
2025-07-17 12:17:59.515 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callduration.range - RANGE
2025-07-17 12:17:59.515 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallDuration, 规则ID=common.CallDuration.range, 规则名称=范围校验
2025-07-17 12:17:59.515 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallDuration 添加了规则: RANGE
2025-07-17 12:17:59.515 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24205, 字段数量=7
2025-07-17 12:17:59.515 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 注册了 7 个字段的元数据
2025-07-17 12:17:59.515 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [CallEndTime, callingPartyNumber, calledPartyNumber, CallBeginTime, mcnnumber, CallDuration, callType] -> VGOP1-R2-10-24205
2025-07-17 12:17:59.515 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 注册了字段到接口的映射关系
2025-07-17 12:17:59.515 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24205 加载了 10 个字段校验规则
2025-07-17 12:17:59.515 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24206 的校验规则...
2025-07-17 12:17:59.515 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.chargetype.enum - ENUM
2025-07-17 12:17:59.515 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=chargetype, 规则ID=common.chargetype.enum, 规则名称=枚举值校验
2025-07-17 12:17:59.515 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 chargetype 添加了规则: ENUM
2025-07-17 12:17:59.515 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.phonenumber.format - FORMAT
2025-07-17 12:17:59.515 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-17 12:17:59.515 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 phonenumber 添加了规则: FORMAT
2025-07-17 12:17:59.515 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.phonenumber.length - LENGTH
2025-07-17 12:17:59.515 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-17 12:17:59.515 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 phonenumber 添加了规则: LENGTH
2025-07-17 12:17:59.515 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.mcnnumber.format - FORMAT
2025-07-17 12:17:59.515 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-17 12:17:59.515 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 mcnnumber 添加了规则: FORMAT
2025-07-17 12:17:59.516 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.sendorrcenum.format - FORMAT
2025-07-17 12:17:59.516 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=sendorreceNum, 规则ID=common.sendorreceNum.format, 规则名称=格式校验
2025-07-17 12:17:59.516 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 sendorreceNum 添加了规则: FORMAT
2025-07-17 12:17:59.516 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.sendorrcenum.length - LENGTH
2025-07-17 12:17:59.516 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=sendorreceNum, 规则ID=common.sendorreceNum.length, 规则名称=长度校验
2025-07-17 12:17:59.516 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 sendorreceNum 添加了规则: LENGTH
2025-07-17 12:17:59.516 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.optime.format - FORMAT
2025-07-17 12:17:59.516 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=optime, 规则ID=common.optime.format, 规则名称=格式校验
2025-07-17 12:17:59.516 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 optime 添加了规则: FORMAT
2025-07-17 12:17:59.516 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24206, 字段数量=5
2025-07-17 12:17:59.516 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 注册了 5 个字段的元数据
2025-07-17 12:17:59.516 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [optime, phonenumber, chargetype, mcnnumber, sendorreceNum] -> VGOP1-R2-10-24206
2025-07-17 12:17:59.516 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 注册了字段到接口的映射关系
2025-07-17 12:17:59.516 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24206 加载了 7 个字段校验规则
2025-07-17 12:17:59.516 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24207 的校验规则...
2025-07-17 12:17:59.516 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.phonenumber.format - FORMAT
2025-07-17 12:17:59.516 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-17 12:17:59.516 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 phonenumber 添加了规则: FORMAT
2025-07-17 12:17:59.516 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.phonenumber.length - LENGTH
2025-07-17 12:17:59.516 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-17 12:17:59.516 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 phonenumber 添加了规则: LENGTH
2025-07-17 12:17:59.516 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnnumber.format - FORMAT
2025-07-17 12:17:59.516 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-17 12:17:59.516 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnnumber 添加了规则: FORMAT
2025-07-17 12:17:59.516 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnnumber.length - LENGTH
2025-07-17 12:17:59.516 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnnumber, 规则ID=common.mcnnumber.length, 规则名称=长度校验
2025-07-17 12:17:59.516 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnnumber 添加了规则: LENGTH
2025-07-17 12:17:59.516 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnimsi.format - FORMAT
2025-07-17 12:17:59.517 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnImsi, 规则ID=common.mcnImsi.format, 规则名称=格式校验
2025-07-17 12:17:59.517 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnImsi 添加了规则: FORMAT
2025-07-17 12:17:59.517 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnimsi.length - LENGTH
2025-07-17 12:17:59.517 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnImsi, 规则ID=common.mcnImsi.length, 规则名称=长度校验
2025-07-17 12:17:59.517 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnImsi 添加了规则: LENGTH
2025-07-17 12:17:59.517 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.businessstate.enum - ENUM
2025-07-17 12:17:59.517 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=businessState, 规则ID=common.businessState.enum, 规则名称=枚举值校验
2025-07-17 12:17:59.517 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 businessState 添加了规则: ENUM
2025-07-17 12:17:59.517 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.numstate.format - FORMAT
2025-07-17 12:17:59.517 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=Numstate, 规则ID=common.Numstate.format, 规则名称=格式校验
2025-07-17 12:17:59.517 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 Numstate 添加了规则: FORMAT
2025-07-17 12:17:59.517 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.locationid.length - LENGTH
2025-07-17 12:17:59.517 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=Locationid, 规则ID=common.Locationid.length, 规则名称=长度校验
2025-07-17 12:17:59.517 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 Locationid 添加了规则: LENGTH
2025-07-17 12:17:59.517 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.bossprovinceid.format - FORMAT
2025-07-17 12:17:59.517 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=BossProvinceid, 规则ID=common.BossProvinceid.format, 规则名称=格式校验
2025-07-17 12:17:59.517 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 BossProvinceid 添加了规则: FORMAT
2025-07-17 12:17:59.517 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.bossprovinceid.range - RANGE
2025-07-17 12:17:59.517 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=BossProvinceid, 规则ID=common.BossProvinceid.range, 规则名称=范围校验
2025-07-17 12:17:59.517 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 BossProvinceid 添加了规则: RANGE
2025-07-17 12:17:59.517 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.openingtime.format - FORMAT
2025-07-17 12:17:59.517 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=openingtime, 规则ID=common.openingtime.format, 规则名称=格式校验
2025-07-17 12:17:59.517 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 openingtime 添加了规则: FORMAT
2025-07-17 12:17:59.518 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24207, 字段数量=8
2025-07-17 12:17:59.518 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 注册了 8 个字段的元数据
2025-07-17 12:17:59.518 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [Numstate, openingtime, mcnImsi, businessState, BossProvinceid, phonenumber, Locationid, mcnnumber] -> VGOP1-R2-10-24207
2025-07-17 12:17:59.518 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 注册了字段到接口的映射关系
2025-07-17 12:17:59.518 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24207 加载了 12 个字段校验规则
2025-07-17 12:17:59.518 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-11-24101 的校验规则...
2025-07-17 12:17:59.518 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.phonenum.format - FORMAT
2025-07-17 12:17:59.518 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=phonenum, 规则ID=common.phonenum.format, 规则名称=格式校验
2025-07-17 12:17:59.518 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 phonenum 添加了规则: FORMAT
2025-07-17 12:17:59.518 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.phonenum.range - RANGE
2025-07-17 12:17:59.518 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=phonenum, 规则ID=common.phonenum.range, 规则名称=范围校验
2025-07-17 12:17:59.518 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 phonenum 添加了规则: RANGE
2025-07-17 12:17:59.518 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnnum.format - FORMAT
2025-07-17 12:17:59.518 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnnum, 规则ID=common.mcnnum.format, 规则名称=格式校验
2025-07-17 12:17:59.518 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnnum 添加了规则: FORMAT
2025-07-17 12:17:59.518 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnnum.range - RANGE
2025-07-17 12:17:59.518 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnnum, 规则ID=common.mcnnum.range, 规则名称=范围校验
2025-07-17 12:17:59.518 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnnum 添加了规则: RANGE
2025-07-17 12:17:59.518 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.appactivenum.format - FORMAT
2025-07-17 12:17:59.518 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=appactivenum, 规则ID=common.appactivenum.format, 规则名称=格式校验
2025-07-17 12:17:59.518 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 appactivenum 添加了规则: FORMAT
2025-07-17 12:17:59.519 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.appactivenum.range - RANGE
2025-07-17 12:17:59.519 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=appactivenum, 规则ID=common.appactivenum.range, 规则名称=范围校验
2025-07-17 12:17:59.519 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 appactivenum 添加了规则: RANGE
2025-07-17 12:17:59.519 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnactivenum.format - FORMAT
2025-07-17 12:17:59.519 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnactivenum, 规则ID=common.mcnactivenum.format, 规则名称=格式校验
2025-07-17 12:17:59.520 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnactivenum 添加了规则: FORMAT
2025-07-17 12:17:59.520 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnactivenum.range - RANGE
2025-07-17 12:17:59.520 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnactivenum, 规则ID=common.mcnactivenum.range, 规则名称=范围校验
2025-07-17 12:17:59.520 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnactivenum 添加了规则: RANGE
2025-07-17 12:17:59.520 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.paynum.format - FORMAT
2025-07-17 12:17:59.520 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=paynum, 规则ID=common.paynum.format, 规则名称=格式校验
2025-07-17 12:17:59.520 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 paynum 添加了规则: FORMAT
2025-07-17 12:17:59.520 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.paynum.range - RANGE
2025-07-17 12:17:59.520 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=paynum, 规则ID=common.paynum.range, 规则名称=范围校验
2025-07-17 12:17:59.520 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 paynum 添加了规则: RANGE
2025-07-17 12:17:59.520 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.feenum.format - FORMAT
2025-07-17 12:17:59.520 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=feenum, 规则ID=common.feenum.format, 规则名称=格式校验
2025-07-17 12:17:59.520 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 feenum 添加了规则: FORMAT
2025-07-17 12:17:59.520 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.feenum.range - RANGE
2025-07-17 12:17:59.521 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=feenum, 规则ID=common.feenum.range, 规则名称=范围校验
2025-07-17 12:17:59.521 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 feenum 添加了规则: RANGE
2025-07-17 12:17:59.521 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secphonenum.format - FORMAT
2025-07-17 12:17:59.521 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secphonenum, 规则ID=common.secphonenum.format, 规则名称=格式校验
2025-07-17 12:17:59.521 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secphonenum 添加了规则: FORMAT
2025-07-17 12:17:59.521 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secphonenum.range - RANGE
2025-07-17 12:17:59.521 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secphonenum, 规则ID=common.secphonenum.range, 规则名称=范围校验
2025-07-17 12:17:59.521 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secphonenum 添加了规则: RANGE
2025-07-17 12:17:59.521 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secmcnnum.format - FORMAT
2025-07-17 12:17:59.521 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secmcnnum, 规则ID=common.secmcnnum.format, 规则名称=格式校验
2025-07-17 12:17:59.521 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secmcnnum 添加了规则: FORMAT
2025-07-17 12:17:59.521 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secmcnnum.range - RANGE
2025-07-17 12:17:59.521 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secmcnnum, 规则ID=common.secmcnnum.range, 规则名称=范围校验
2025-07-17 12:17:59.522 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secmcnnum 添加了规则: RANGE
2025-07-17 12:17:59.522 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.amcnnum.format - FORMAT
2025-07-17 12:17:59.522 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=amcnnum, 规则ID=common.amcnnum.format, 规则名称=格式校验
2025-07-17 12:17:59.522 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 amcnnum 添加了规则: FORMAT
2025-07-17 12:17:59.522 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.amcnnum.range - RANGE
2025-07-17 12:17:59.522 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=amcnnum, 规则ID=common.amcnnum.range, 规则名称=范围校验
2025-07-17 12:17:59.522 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 amcnnum 添加了规则: RANGE
2025-07-17 12:17:59.522 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-11-24101, 字段数量=9
2025-07-17 12:17:59.522 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 注册了 9 个字段的元数据
2025-07-17 12:17:59.522 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [appactivenum, paynum, mcnnum, secphonenum, phonenum, mcnactivenum, secmcnnum, amcnnum, feenum] -> VGOP1-R2-11-24101
2025-07-17 12:17:59.522 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 注册了字段到接口的映射关系
2025-07-17 12:17:59.522 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-11-24101 加载了 18 个字段校验规则
2025-07-17 12:17:59.522 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-13-24301 的校验规则...
2025-07-17 12:17:59.522 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servid.format - FORMAT
2025-07-17 12:17:59.522 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServID, 规则ID=common.ServID.format, 规则名称=格式校验
2025-07-17 12:17:59.522 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServID 添加了规则: FORMAT
2025-07-17 12:17:59.522 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servid.length - LENGTH
2025-07-17 12:17:59.522 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServID, 规则ID=common.ServID.length, 规则名称=长度校验
2025-07-17 12:17:59.522 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServID 添加了规则: LENGTH
2025-07-17 12:17:59.522 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servid.standard_format - LENGTH
2025-07-17 12:17:59.522 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServID, 规则ID=common.ServID.length, 规则名称=长度校验
2025-07-17 12:17:59.522 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServID 添加了规则: LENGTH
2025-07-17 12:17:59.522 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servname.length - LENGTH
2025-07-17 12:17:59.522 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServName, 规则ID=common.ServName.length, 规则名称=长度校验
2025-07-17 12:17:59.522 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServName 添加了规则: LENGTH
2025-07-17 12:17:59.522 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servname.content - FORMAT
2025-07-17 12:17:59.523 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServName, 规则ID=common.ServName.format, 规则名称=格式校验
2025-07-17 12:17:59.523 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServName 添加了规则: FORMAT
2025-07-17 12:17:59.523 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-13-24301, 字段数量=2
2025-07-17 12:17:59.523 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 注册了 2 个字段的元数据
2025-07-17 12:17:59.523 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [ServName, ServID] -> VGOP1-R2-13-24301
2025-07-17 12:17:59.523 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 注册了字段到接口的映射关系
2025-07-17 12:17:59.523 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-13-24301 加载了 5 个字段校验规则
2025-07-17 12:17:59.523 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-13-24302 的校验规则...
2025-07-17 12:17:59.523 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelcode.format - FORMAT
2025-07-17 12:17:59.523 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelcode, 规则ID=common.channelcode.format, 规则名称=格式校验
2025-07-17 12:17:59.523 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelcode 添加了规则: FORMAT
2025-07-17 12:17:59.523 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelcode.length - LENGTH
2025-07-17 12:17:59.523 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelcode, 规则ID=common.channelcode.length, 规则名称=长度校验
2025-07-17 12:17:59.523 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelcode 添加了规则: LENGTH
2025-07-17 12:17:59.523 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelcode.range - RANGE
2025-07-17 12:17:59.523 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelcode, 规则ID=common.channelcode.range, 规则名称=范围校验
2025-07-17 12:17:59.523 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelcode 添加了规则: RANGE
2025-07-17 12:17:59.523 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelname.length - LENGTH
2025-07-17 12:17:59.524 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelname, 规则ID=common.channelname.length, 规则名称=长度校验
2025-07-17 12:17:59.524 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelname 添加了规则: LENGTH
2025-07-17 12:17:59.524 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelname.content - FORMAT
2025-07-17 12:17:59.524 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelname, 规则ID=common.channelname.format, 规则名称=格式校验
2025-07-17 12:17:59.524 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelname 添加了规则: FORMAT
2025-07-17 12:17:59.524 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-13-24302, 字段数量=2
2025-07-17 12:17:59.524 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 注册了 2 个字段的元数据
2025-07-17 12:17:59.524 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [channelname, channelcode] -> VGOP1-R2-13-24302
2025-07-17 12:17:59.524 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 注册了字段到接口的映射关系
2025-07-17 12:17:59.524 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-13-24302 加载了 5 个字段校验规则
2025-07-17 12:17:59.524 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-13-24303 的校验规则...
2025-07-17 12:17:59.524 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdown.format - FORMAT
2025-07-17 12:17:59.524 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdown, 规则ID=common.shutdown.format, 规则名称=格式校验
2025-07-17 12:17:59.524 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdown 添加了规则: FORMAT
2025-07-17 12:17:59.524 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdown.length - LENGTH
2025-07-17 12:17:59.524 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdown, 规则ID=common.shutdown.length, 规则名称=长度校验
2025-07-17 12:17:59.524 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdown 添加了规则: LENGTH
2025-07-17 12:17:59.524 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdown.range - RANGE
2025-07-17 12:17:59.524 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdown, 规则ID=common.shutdown.range, 规则名称=范围校验
2025-07-17 12:17:59.524 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdown 添加了规则: RANGE
2025-07-17 12:17:59.524 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdownname.length - LENGTH
2025-07-17 12:17:59.524 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdownname, 规则ID=common.shutdownname.length, 规则名称=长度校验
2025-07-17 12:17:59.524 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdownname 添加了规则: LENGTH
2025-07-17 12:17:59.524 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdownname.content - FORMAT
2025-07-17 12:17:59.524 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdownname, 规则ID=common.shutdownname.format, 规则名称=格式校验
2025-07-17 12:17:59.525 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdownname 添加了规则: FORMAT
2025-07-17 12:17:59.525 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-13-24303, 字段数量=2
2025-07-17 12:17:59.525 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 注册了 2 个字段的元数据
2025-07-17 12:17:59.525 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [shutdownname, shutdown] -> VGOP1-R2-13-24303
2025-07-17 12:17:59.525 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 注册了字段到接口的映射关系
2025-07-17 12:17:59.525 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-13-24303 加载了 5 个字段校验规则
2025-07-17 12:17:59.525 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 校验规则加载完成，共加载 100 个规则
2025-07-17 12:18:00.891 [RMI TCP Connection(11)-************] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:18:00.892 [RMI TCP Connection(11)-************] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:18:00.892 [RMI TCP Connection(11)-************] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:18:00.893 [RMI TCP Connection(11)-************] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:18:01.711 [RMI TCP Connection(13)-************] INFO  o.a.c.c.C.[.[localhost].[/vgop] [||||] [] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-17 12:18:01.711 [RMI TCP Connection(13)-************] INFO  o.s.web.servlet.DispatcherServlet [||||] [] - Initializing Servlet 'dispatcherServlet'
2025-07-17 12:18:01.712 [RMI TCP Connection(13)-************] INFO  o.s.web.servlet.DispatcherServlet [||||] [] - Completed initialization in 0 ms
2025-07-17 12:33:36.029 [Thread-527] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-17 12:33:36.789 [Thread-527] INFO  o.s.s.quartz.SchedulerFactoryBean [||||] [] - Shutting down Quartz Scheduler
2025-07-17 12:33:36.789 [Thread-527] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-17 12:33:36.789 [Thread-527] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-17 12:33:36.790 [Thread-527] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-17 12:33:36.794 [Thread-527] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-2} closing ...
2025-07-17 12:33:36.857 [Thread-527] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-2} closed
2025-07-17 12:33:36.858 [Thread-527] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-1} closing ...
2025-07-17 12:33:36.917 [Thread-527] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-1} closed
2025-07-17 12:33:37.596 [restartedMain] INFO  c.v.service.VgopServiceApplication [||||] [] - Starting VgopServiceApplication using Java 1.8.0_452 on ziber with PID 25692 (C:\workspaces\hdh\vgop-vli\vgop-service\target\classes started by galil in C:\workspaces\hdh\vgop-vli\vgop-service)
2025-07-17 12:33:37.598 [restartedMain] DEBUG c.v.service.VgopServiceApplication [||||] [] - Running with Spring Boot v2.5.5, Spring v5.3.19
2025-07-17 12:33:37.598 [restartedMain] INFO  c.v.service.VgopServiceApplication [||||] [] - The following profiles are active: dev
2025-07-17 12:33:38.665 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\DataExportMapper.class]
2025-07-17 12:33:38.665 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\RevisionMapper.class]
2025-07-17 12:33:38.665 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\ValidationAlertsMapper.class]
2025-07-17 12:33:38.666 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\primary\RevisionTimesMapper.class]
2025-07-17 12:33:38.666 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\secondary\TaskExecutionMapper.class]
2025-07-17 12:33:38.666 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'dataExportMapper' and 'com.vgop.service.dao.DataExportMapper' mapperInterface
2025-07-17 12:33:38.666 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'revisionMapper' and 'com.vgop.service.dao.RevisionMapper' mapperInterface
2025-07-17 12:33:38.666 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'validationAlertsMapper' and 'com.vgop.service.dao.ValidationAlertsMapper' mapperInterface
2025-07-17 12:33:38.668 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'revisionTimesMapper' and 'com.vgop.service.dao.primary.RevisionTimesMapper' mapperInterface
2025-07-17 12:33:38.668 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'taskExecutionMapper' and 'com.vgop.service.dao.secondary.TaskExecutionMapper' mapperInterface
2025-07-17 12:33:38.716 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [||||] [] - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-17 12:33:39.001 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer [||||] [] - Tomcat initialized with port(s): 8080 (http)
2025-07-17 12:33:39.001 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol [||||] [] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-17 12:33:39.002 [restartedMain] INFO  o.a.catalina.core.StandardService [||||] [] - Starting service [Tomcat]
2025-07-17 12:33:39.002 [restartedMain] INFO  o.a.catalina.core.StandardEngine [||||] [] - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-17 12:33:39.061 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/vgop] [||||] [] - Initializing Spring embedded WebApplicationContext
2025-07-17 12:33:39.061 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext [||||] [] - Root WebApplicationContext: initialization completed in 1456 ms
2025-07-17 12:33:39.124 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - ValidationEngine已初始化，等待规则配置...
2025-07-17 12:33:40.022 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\mapper\DataExportMapper.xml]'
2025-07-17 12:33:40.028 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\mapper\RevisionMapper.xml]'
2025-07-17 12:33:40.036 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\mapper\TaskExecutionMapper.xml]'
2025-07-17 12:33:40.045 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\mapper\ValidationAlertsMapper.xml]'
2025-07-17 12:33:40.166 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - === 开始测试数据库连接 ===
2025-07-17 12:33:40.166 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 测试主数据源连接...
2025-07-17 12:33:40.167 [restartedMain] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - testWhileIdle is true, validationQuery not set
2025-07-17 12:33:40.169 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-3} inited
2025-07-17 12:33:40.765 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 主数据源连接成功: {databaseProductVersion=12.10.FC4G1AEE, databaseProductName=GBase Server, driverVersion=4.10.JC4G1N999, name=主数据源, driverName=GBase JDBC Driver for GBase Server, userName=ismp, url=jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;}
2025-07-17 12:33:40.766 [restartedMain] INFO  com.vgop.service.util.DatabaseUtil [||||] [] - 检测到数据库产品: GBase Server, URL: jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;
2025-07-17 12:33:40.767 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 检测到主数据库类型: gbase
2025-07-17 12:33:40.767 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 是否支持UNLOAD命令: true
2025-07-17 12:33:40.767 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 测试次数据源连接...
2025-07-17 12:33:40.767 [restartedMain] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - testWhileIdle is true, validationQuery not set
2025-07-17 12:33:40.768 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-4} inited
2025-07-17 12:33:41.341 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 次数据源连接成功: {databaseProductVersion=12.10.FC4G1AEE, databaseProductName=GBase Server, driverVersion=4.10.JC4G1N999, name=次数据源, driverName=GBase JDBC Driver for GBase Server, userName=ismp, url=jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;}
2025-07-17 12:33:41.341 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - === 数据库连接测试完成 ===
2025-07-17 12:33:41.509 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory [||||] [] - Using default implementation for ThreadExecutor
2025-07-17 12:33:41.510 [restartedMain] INFO  o.quartz.core.SchedulerSignalerImpl [||||] [] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-17 12:33:41.510 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - Quartz Scheduler v.2.3.2 created.
2025-07-17 12:33:41.510 [restartedMain] INFO  org.quartz.simpl.RAMJobStore [||||] [] - RAMJobStore initialized.
2025-07-17 12:33:41.510 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-17 12:33:41.510 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory [||||] [] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-17 12:33:41.510 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory [||||] [] - Quartz scheduler version: 2.3.2
2025-07-17 12:33:41.510 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3d3f42e0
2025-07-17 12:33:41.545 [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver [||||] [] - Exposing 15 endpoint(s) beneath base path '/actuator'
2025-07-17 12:33:41.587 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:33:41.587 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库产品名称: GBase Server, 版本: 12.10.FC4G1AEE
2025-07-17 12:33:41.587 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 检测到数据库类型: GBase Server
2025-07-17 12:33:41.587 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 配置GBase数据库字符编码设置
2025-07-17 12:33:41.587 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]
2025-07-17 12:33:41.587 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:33:41.646 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Looking up default SQLErrorCodes for DataSource [com.alibaba.druid.pool.DruidDataSource@40b7a2d3]
2025-07-17 12:33:41.646 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:33:41.647 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - SQL error codes for 'GBase Server' not found
2025-07-17 12:33:41.647 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Caching SQL error codes for DataSource [com.alibaba.druid.pool.DruidDataSource@40b7a2d3]: database product name is 'GBase Server'
2025-07-17 12:33:41.647 [restartedMain] DEBUG o.s.j.s.SQLErrorCodeSQLExceptionTranslator [||||] [] - Unable to translate SQLException with Error code '-728', will now try the fallback translator
2025-07-17 12:33:41.647 [restartedMain] DEBUG o.s.j.s.SQLStateSQLExceptionTranslator [||||] [] - Extracted SQL state class 'IX' from value 'IX000'
2025-07-17 12:33:41.647 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 查询GBase数据库字符编码信息失败: StatementCallback; uncategorized SQLException for SQL [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]; SQL state [IX000]; error code [-728]; Unknown first argument of dbinfo(dblocale).; nested exception is java.sql.SQLException: Unknown first argument of dbinfo(dblocale).
2025-07-17 12:33:41.647 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - GBase数据库字符编码配置完成，依赖JDBC URL参数: DB_LOCALE, NEWCODESET
2025-07-17 12:33:41.647 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dbhostname') FROM systables WHERE tabid = 1]
2025-07-17 12:33:41.647 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:33:41.929 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库主机名: eb33207
2025-07-17 12:33:41.929 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT LENGTH('测试中文字符') FROM systables WHERE tabid = 1]
2025-07-17 12:33:41.929 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:33:42.214 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 字符编码兼容性测试通过，中文字符长度: 12
2025-07-17 12:33:42.214 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 已配置主数据源字符编码安全的JdbcTemplate
2025-07-17 12:33:42.216 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:33:42.216 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库产品名称: GBase Server, 版本: 12.10.FC4G1AEE
2025-07-17 12:33:42.216 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 检测到数据库类型: GBase Server
2025-07-17 12:33:42.216 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 配置GBase数据库字符编码设置
2025-07-17 12:33:42.216 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]
2025-07-17 12:33:42.216 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:33:42.273 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Looking up default SQLErrorCodes for DataSource [com.alibaba.druid.pool.DruidDataSource@50b6d6eb]
2025-07-17 12:33:42.281 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:33:42.282 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - SQL error codes for 'GBase Server' not found
2025-07-17 12:33:42.282 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Caching SQL error codes for DataSource [com.alibaba.druid.pool.DruidDataSource@50b6d6eb]: database product name is 'GBase Server'
2025-07-17 12:33:42.282 [restartedMain] DEBUG o.s.j.s.SQLErrorCodeSQLExceptionTranslator [||||] [] - Unable to translate SQLException with Error code '-728', will now try the fallback translator
2025-07-17 12:33:42.282 [restartedMain] DEBUG o.s.j.s.SQLStateSQLExceptionTranslator [||||] [] - Extracted SQL state class 'IX' from value 'IX000'
2025-07-17 12:33:42.282 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 查询GBase数据库字符编码信息失败: StatementCallback; uncategorized SQLException for SQL [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]; SQL state [IX000]; error code [-728]; Unknown first argument of dbinfo(dblocale).; nested exception is java.sql.SQLException: Unknown first argument of dbinfo(dblocale).
2025-07-17 12:33:42.282 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - GBase数据库字符编码配置完成，依赖JDBC URL参数: DB_LOCALE, NEWCODESET
2025-07-17 12:33:42.282 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dbhostname') FROM systables WHERE tabid = 1]
2025-07-17 12:33:42.282 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:33:42.574 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库主机名: eb33207
2025-07-17 12:33:42.574 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT LENGTH('测试中文字符') FROM systables WHERE tabid = 1]
2025-07-17 12:33:42.574 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:33:42.860 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 字符编码兼容性测试通过，中文字符长度: 12
2025-07-17 12:33:42.860 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 已配置次数据源字符编码安全的JdbcTemplate
2025-07-17 12:33:42.910 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer [||||] [] - LiveReload server is running on port 35729
2025-07-17 12:33:43.032 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol [||||] [] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-17 12:33:43.036 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer [||||] [] - Tomcat started on port(s): 8080 (http) with context path '/vgop'
2025-07-17 12:33:43.269 [restartedMain] INFO  o.s.s.quartz.SchedulerFactoryBean [||||] [] - Starting Quartz Scheduler now
2025-07-17 12:33:43.269 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-17 12:33:43.279 [restartedMain] INFO  c.v.service.VgopServiceApplication [||||] [] - Started VgopServiceApplication in 6.137 seconds (JVM running for 1212.978)
2025-07-17 12:33:43.281 [restartedMain] INFO  c.v.s.s.ConfigValidationService [||||] [] - 开始验证应用配置...
2025-07-17 12:33:43.282 [restartedMain] INFO  c.v.s.s.ConfigValidationService [||||] [] - 配置验证通过
2025-07-17 12:33:43.283 [restartedMain] INFO  c.v.s.service.DirectoryInitService [||||] [] - 开始初始化目录结构...
2025-07-17 12:33:43.283 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 数据导出根目录 - ./VGOPdata/datafile/
2025-07-17 12:33:43.283 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日志根目录 - ./logs/
2025-07-17 12:33:43.284 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 备份根目录 - ./data/backup/
2025-07-17 12:33:43.284 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 告警文件目录 - ./data/alerts/
2025-07-17 12:33:43.284 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250714 - ./VGOPdata/datafile//20250714/day
2025-07-17 12:33:43.285 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250714 - ./VGOPdata/datafile//20250714/month
2025-07-17 12:33:43.285 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250715 - ./VGOPdata/datafile//20250715/day
2025-07-17 12:33:43.285 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250715 - ./VGOPdata/datafile//20250715/month
2025-07-17 12:33:43.286 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250716 - ./VGOPdata/datafile//20250716/day
2025-07-17 12:33:43.287 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250716 - ./VGOPdata/datafile//20250716/month
2025-07-17 12:33:43.287 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250717 - ./VGOPdata/datafile//20250717/day
2025-07-17 12:33:43.288 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250717 - ./VGOPdata/datafile//20250717/month
2025-07-17 12:33:43.288 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250718 - ./VGOPdata/datafile//20250718/day
2025-07-17 12:33:43.288 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250718 - ./VGOPdata/datafile//20250718/month
2025-07-17 12:33:43.288 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250719 - ./VGOPdata/datafile//20250719/day
2025-07-17 12:33:43.288 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250719 - ./VGOPdata/datafile//20250719/month
2025-07-17 12:33:43.288 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月度数据目录 - 202507 - ./VGOPdata/datafile//202507
2025-07-17 12:33:43.289 [restartedMain] INFO  c.v.s.service.DirectoryInitService [||||] [] - 目录结构初始化完成
2025-07-17 12:33:43.291 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener [||||] [] - Condition evaluation unchanged
2025-07-17 12:33:43.291 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 开始从配置文件加载校验规则...
2025-07-17 12:33:43.291 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24201 的校验规则...
2025-07-17 12:33:43.291 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phonenumber.format - FORMAT
2025-07-17 12:33:43.292 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-17 12:33:43.292 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phonenumber 添加了规则: FORMAT
2025-07-17 12:33:43.292 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phonenumber.length - LENGTH
2025-07-17 12:33:43.292 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-17 12:33:43.292 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phonenumber 添加了规则: LENGTH
2025-07-17 12:33:43.292 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phonestate.enum - ENUM
2025-07-17 12:33:43.292 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phonestate, 规则ID=common.phonestate.enum, 规则名称=枚举值校验
2025-07-17 12:33:43.292 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phonestate 添加了规则: ENUM
2025-07-17 12:33:43.292 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phoneimsi.length - LENGTH
2025-07-17 12:33:43.292 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phoneimsi, 规则ID=common.phoneimsi.length, 规则名称=长度校验
2025-07-17 12:33:43.292 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phoneimsi 添加了规则: LENGTH
2025-07-17 12:33:43.293 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phoneimei.length - LENGTH
2025-07-17 12:33:43.293 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phoneimei, 规则ID=common.phoneimei.length, 规则名称=长度校验
2025-07-17 12:33:43.293 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phoneimei 添加了规则: LENGTH
2025-07-17 12:33:43.293 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.locationid.length - LENGTH
2025-07-17 12:33:43.293 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=locationid, 规则ID=common.locationid.length, 规则名称=长度校验
2025-07-17 12:33:43.293 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 locationid 添加了规则: LENGTH
2025-07-17 12:33:43.293 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.provinceid.length - LENGTH
2025-07-17 12:33:43.293 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=provinceid, 规则ID=common.provinceid.length, 规则名称=长度校验
2025-07-17 12:33:43.293 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 provinceid 添加了规则: LENGTH
2025-07-17 12:33:43.293 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.openingtime.format - FORMAT
2025-07-17 12:33:43.293 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=openingtime, 规则ID=common.openingtime.format, 规则名称=格式校验
2025-07-17 12:33:43.293 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 openingtime 添加了规则: FORMAT
2025-07-17 12:33:43.293 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.optime.format - FORMAT
2025-07-17 12:33:43.293 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=Optime, 规则ID=common.Optime.format, 规则名称=格式校验
2025-07-17 12:33:43.293 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 Optime 添加了规则: FORMAT
2025-07-17 12:33:43.293 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.sex.enum - ENUM
2025-07-17 12:33:43.293 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=sex, 规则ID=common.sex.enum, 规则名称=枚举值校验
2025-07-17 12:33:43.294 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 sex 添加了规则: ENUM
2025-07-17 12:33:43.294 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24201, 字段数量=9
2025-07-17 12:33:43.294 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 注册了 9 个字段的元数据
2025-07-17 12:33:43.294 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [phonestate, Optime, phoneimei, openingtime, locationid, sex, phonenumber, phoneimsi, provinceid] -> VGOP1-R2-10-24201
2025-07-17 12:33:43.294 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 注册了字段到接口的映射关系
2025-07-17 12:33:43.294 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24201 加载了 10 个字段校验规则
2025-07-17 12:33:43.294 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24202 的校验规则...
2025-07-17 12:33:43.294 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnumber.format - FORMAT
2025-07-17 12:33:43.294 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-17 12:33:43.294 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnumber 添加了规则: FORMAT
2025-07-17 12:33:43.294 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnumber.length - LENGTH
2025-07-17 12:33:43.294 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnumber, 规则ID=common.mcnnumber.length, 规则名称=长度校验
2025-07-17 12:33:43.294 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnumber 添加了规则: LENGTH
2025-07-17 12:33:43.294 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.phonenumber.format - FORMAT
2025-07-17 12:33:43.294 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-17 12:33:43.294 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 phonenumber 添加了规则: FORMAT
2025-07-17 12:33:43.294 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.phonenumber.length - LENGTH
2025-07-17 12:33:43.294 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-17 12:33:43.294 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 phonenumber 添加了规则: LENGTH
2025-07-17 12:33:43.294 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.business.enum - ENUM
2025-07-17 12:33:43.295 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=business, 规则ID=common.business.enum, 规则名称=枚举值校验
2025-07-17 12:33:43.295 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 business 添加了规则: ENUM
2025-07-17 12:33:43.295 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.shutdown.length - LENGTH
2025-07-17 12:33:43.295 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=shutdown, 规则ID=common.shutdown.length, 规则名称=长度校验
2025-07-17 12:33:43.295 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 shutdown 添加了规则: LENGTH
2025-07-17 12:33:43.295 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnimsi.length - LENGTH
2025-07-17 12:33:43.295 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnimsi, 规则ID=common.mcnimsi.length, 规则名称=长度校验
2025-07-17 12:33:43.295 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnimsi 添加了规则: LENGTH
2025-07-17 12:33:43.295 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnimsi.format - FORMAT
2025-07-17 12:33:43.295 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnimsi, 规则ID=common.mcnimsi.format, 规则名称=格式校验
2025-07-17 12:33:43.295 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnimsi 添加了规则: FORMAT
2025-07-17 12:33:43.295 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnlocationid.length - LENGTH
2025-07-17 12:33:43.295 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnlocationid, 规则ID=common.mcnlocationid.length, 规则名称=长度校验
2025-07-17 12:33:43.295 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnlocationid 添加了规则: LENGTH
2025-07-17 12:33:43.295 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.numstate.length - LENGTH
2025-07-17 12:33:43.295 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=numstate, 规则ID=common.numstate.length, 规则名称=长度校验
2025-07-17 12:33:43.295 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 numstate 添加了规则: LENGTH
2025-07-17 12:33:43.295 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnature.enum - ENUM
2025-07-17 12:33:43.295 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnature, 规则ID=common.mcnnature.enum, 规则名称=枚举值校验
2025-07-17 12:33:43.295 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnature 添加了规则: ENUM
2025-07-17 12:33:43.295 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnum.enum - ENUM
2025-07-17 12:33:43.295 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnum, 规则ID=common.mcnnum.enum, 规则名称=枚举值校验
2025-07-17 12:33:43.295 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnum 添加了规则: ENUM
2025-07-17 12:33:43.295 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnum.length - LENGTH
2025-07-17 12:33:43.295 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnum, 规则ID=common.mcnnum.length, 规则名称=长度校验
2025-07-17 12:33:43.295 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnum 添加了规则: LENGTH
2025-07-17 12:33:43.295 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.channel.length - LENGTH
2025-07-17 12:33:43.296 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=channel, 规则ID=common.channel.length, 规则名称=长度校验
2025-07-17 12:33:43.296 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 channel 添加了规则: LENGTH
2025-07-17 12:33:43.296 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mj.enum - ENUM
2025-07-17 12:33:43.296 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mj, 规则ID=common.mj.enum, 规则名称=枚举值校验
2025-07-17 12:33:43.296 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mj 添加了规则: ENUM
2025-07-17 12:33:43.296 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.openingtime.format - FORMAT
2025-07-17 12:33:43.296 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=openingtime, 规则ID=common.openingtime.format, 规则名称=格式校验
2025-07-17 12:33:43.296 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 openingtime 添加了规则: FORMAT
2025-07-17 12:33:43.296 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.optime.format - FORMAT
2025-07-17 12:33:43.296 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=Optime, 规则ID=common.Optime.format, 规则名称=格式校验
2025-07-17 12:33:43.296 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 Optime 添加了规则: FORMAT
2025-07-17 12:33:43.296 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcimsitime.format - FORMAT
2025-07-17 12:33:43.296 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcimsitime, 规则ID=common.mcimsitime.format, 规则名称=格式校验
2025-07-17 12:33:43.296 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcimsitime 添加了规则: FORMAT
2025-07-17 12:33:43.296 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.usertype.length - LENGTH
2025-07-17 12:33:43.296 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=usertype, 规则ID=common.usertype.length, 规则名称=长度校验
2025-07-17 12:33:43.296 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 usertype 添加了规则: LENGTH
2025-07-17 12:33:43.296 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.begintime.format - FORMAT
2025-07-17 12:33:43.296 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=Begintime, 规则ID=common.Begintime.format, 规则名称=格式校验
2025-07-17 12:33:43.296 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 Begintime 添加了规则: FORMAT
2025-07-17 12:33:43.296 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.endtime.format - FORMAT
2025-07-17 12:33:43.296 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=Endtime, 规则ID=common.Endtime.format, 规则名称=格式校验
2025-07-17 12:33:43.296 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 Endtime 添加了规则: FORMAT
2025-07-17 12:33:43.296 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.servid.length - LENGTH
2025-07-17 12:33:43.296 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=ServID, 规则ID=common.ServID.length, 规则名称=长度校验
2025-07-17 12:33:43.296 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 ServID 添加了规则: LENGTH
2025-07-17 12:33:43.296 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24202, 字段数量=18
2025-07-17 12:33:43.296 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 注册了 18 个字段的元数据
2025-07-17 12:33:43.296 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [mcnlocationid, Optime, business, mcnimsi, mcimsitime, phonenumber, mcnnum, channel, usertype, numstate, Endtime, openingtime, ServID, mcnnature, Begintime, mcnnumber, mj, shutdown] -> VGOP1-R2-10-24202
2025-07-17 12:33:43.297 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 注册了字段到接口的映射关系
2025-07-17 12:33:43.297 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24202 加载了 22 个字段校验规则
2025-07-17 12:33:43.297 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24203 的校验规则...
2025-07-17 12:33:43.297 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.account_id.length - LENGTH
2025-07-17 12:33:43.297 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=account_id, 规则ID=common.account_id.length, 规则名称=长度校验
2025-07-17 12:33:43.297 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 account_id 添加了规则: LENGTH
2025-07-17 12:33:43.297 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.phonenumber.format - FORMAT
2025-07-17 12:33:43.297 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-17 12:33:43.297 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 phonenumber 添加了规则: FORMAT
2025-07-17 12:33:43.297 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.phonenumber.length - LENGTH
2025-07-17 12:33:43.297 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-17 12:33:43.297 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 phonenumber 添加了规则: LENGTH
2025-07-17 12:33:43.297 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.type.enum - ENUM
2025-07-17 12:33:43.297 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=type, 规则ID=common.type.enum, 规则名称=枚举值校验
2025-07-17 12:33:43.297 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 type 添加了规则: ENUM
2025-07-17 12:33:43.297 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.optime.format - FORMAT
2025-07-17 12:33:43.297 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=optime, 规则ID=common.optime.format, 规则名称=格式校验
2025-07-17 12:33:43.297 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 optime 添加了规则: FORMAT
2025-07-17 12:33:43.297 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.version.length - LENGTH
2025-07-17 12:33:43.297 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=version, 规则ID=common.version.length, 规则名称=长度校验
2025-07-17 12:33:43.297 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 version 添加了规则: LENGTH
2025-07-17 12:33:43.297 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24203, 字段数量=5
2025-07-17 12:33:43.297 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 注册了 5 个字段的元数据
2025-07-17 12:33:43.297 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [account_id, optime, phonenumber, type, version] -> VGOP1-R2-10-24203
2025-07-17 12:33:43.298 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 注册了字段到接口的映射关系
2025-07-17 12:33:43.298 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24203 加载了 6 个字段校验规则
2025-07-17 12:33:43.298 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24205 的校验规则...
2025-07-17 12:33:43.298 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.calltype.enum - ENUM
2025-07-17 12:33:43.298 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=callType, 规则ID=common.callType.enum, 规则名称=枚举值校验
2025-07-17 12:33:43.298 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 callType 添加了规则: ENUM
2025-07-17 12:33:43.298 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callingpartynumber.format - FORMAT
2025-07-17 12:33:43.298 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=callingPartyNumber, 规则ID=common.callingPartyNumber.format, 规则名称=格式校验
2025-07-17 12:33:43.298 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 callingPartyNumber 添加了规则: FORMAT
2025-07-17 12:33:43.298 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callingpartynumber.length - LENGTH
2025-07-17 12:33:43.298 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=callingPartyNumber, 规则ID=common.callingPartyNumber.length, 规则名称=长度校验
2025-07-17 12:33:43.298 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 callingPartyNumber 添加了规则: LENGTH
2025-07-17 12:33:43.298 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.calledpartynumber.format - FORMAT
2025-07-17 12:33:43.298 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=calledPartyNumber, 规则ID=common.calledPartyNumber.format, 规则名称=格式校验
2025-07-17 12:33:43.298 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 calledPartyNumber 添加了规则: FORMAT
2025-07-17 12:33:43.298 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.calledpartynumber.length - LENGTH
2025-07-17 12:33:43.298 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=calledPartyNumber, 规则ID=common.calledPartyNumber.length, 规则名称=长度校验
2025-07-17 12:33:43.298 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 calledPartyNumber 添加了规则: LENGTH
2025-07-17 12:33:43.298 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.mcnnumber.format - FORMAT
2025-07-17 12:33:43.298 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-17 12:33:43.298 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 mcnnumber 添加了规则: FORMAT
2025-07-17 12:33:43.298 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callbegintime.format - FORMAT
2025-07-17 12:33:43.298 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallBeginTime, 规则ID=common.CallBeginTime.format, 规则名称=格式校验
2025-07-17 12:33:43.298 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallBeginTime 添加了规则: FORMAT
2025-07-17 12:33:43.298 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callendtime.format - FORMAT
2025-07-17 12:33:43.298 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallEndTime, 规则ID=common.CallEndTime.format, 规则名称=格式校验
2025-07-17 12:33:43.299 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallEndTime 添加了规则: FORMAT
2025-07-17 12:33:43.299 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callduration.format - FORMAT
2025-07-17 12:33:43.299 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallDuration, 规则ID=common.CallDuration.format, 规则名称=格式校验
2025-07-17 12:33:43.299 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallDuration 添加了规则: FORMAT
2025-07-17 12:33:43.299 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callduration.range - RANGE
2025-07-17 12:33:43.299 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallDuration, 规则ID=common.CallDuration.range, 规则名称=范围校验
2025-07-17 12:33:43.299 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallDuration 添加了规则: RANGE
2025-07-17 12:33:43.299 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24205, 字段数量=7
2025-07-17 12:33:43.299 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 注册了 7 个字段的元数据
2025-07-17 12:33:43.299 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [CallEndTime, callingPartyNumber, calledPartyNumber, CallBeginTime, mcnnumber, CallDuration, callType] -> VGOP1-R2-10-24205
2025-07-17 12:33:43.299 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 注册了字段到接口的映射关系
2025-07-17 12:33:43.299 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24205 加载了 10 个字段校验规则
2025-07-17 12:33:43.299 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24206 的校验规则...
2025-07-17 12:33:43.299 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.chargetype.enum - ENUM
2025-07-17 12:33:43.299 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=chargetype, 规则ID=common.chargetype.enum, 规则名称=枚举值校验
2025-07-17 12:33:43.299 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 chargetype 添加了规则: ENUM
2025-07-17 12:33:43.299 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.phonenumber.format - FORMAT
2025-07-17 12:33:43.299 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-17 12:33:43.299 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 phonenumber 添加了规则: FORMAT
2025-07-17 12:33:43.299 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.phonenumber.length - LENGTH
2025-07-17 12:33:43.299 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-17 12:33:43.299 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 phonenumber 添加了规则: LENGTH
2025-07-17 12:33:43.299 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.mcnnumber.format - FORMAT
2025-07-17 12:33:43.300 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-17 12:33:43.300 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 mcnnumber 添加了规则: FORMAT
2025-07-17 12:33:43.300 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.sendorrcenum.format - FORMAT
2025-07-17 12:33:43.300 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=sendorreceNum, 规则ID=common.sendorreceNum.format, 规则名称=格式校验
2025-07-17 12:33:43.300 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 sendorreceNum 添加了规则: FORMAT
2025-07-17 12:33:43.300 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.sendorrcenum.length - LENGTH
2025-07-17 12:33:43.300 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=sendorreceNum, 规则ID=common.sendorreceNum.length, 规则名称=长度校验
2025-07-17 12:33:43.300 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 sendorreceNum 添加了规则: LENGTH
2025-07-17 12:33:43.300 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.optime.format - FORMAT
2025-07-17 12:33:43.300 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=optime, 规则ID=common.optime.format, 规则名称=格式校验
2025-07-17 12:33:43.300 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 optime 添加了规则: FORMAT
2025-07-17 12:33:43.300 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24206, 字段数量=5
2025-07-17 12:33:43.300 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 注册了 5 个字段的元数据
2025-07-17 12:33:43.300 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [optime, phonenumber, chargetype, mcnnumber, sendorreceNum] -> VGOP1-R2-10-24206
2025-07-17 12:33:43.300 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 注册了字段到接口的映射关系
2025-07-17 12:33:43.300 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24206 加载了 7 个字段校验规则
2025-07-17 12:33:43.300 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24207 的校验规则...
2025-07-17 12:33:43.300 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.phonenumber.format - FORMAT
2025-07-17 12:33:43.300 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-17 12:33:43.300 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 phonenumber 添加了规则: FORMAT
2025-07-17 12:33:43.300 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.phonenumber.length - LENGTH
2025-07-17 12:33:43.300 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-17 12:33:43.300 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 phonenumber 添加了规则: LENGTH
2025-07-17 12:33:43.300 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnnumber.format - FORMAT
2025-07-17 12:33:43.300 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-17 12:33:43.300 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnnumber 添加了规则: FORMAT
2025-07-17 12:33:43.300 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnnumber.length - LENGTH
2025-07-17 12:33:43.300 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnnumber, 规则ID=common.mcnnumber.length, 规则名称=长度校验
2025-07-17 12:33:43.301 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnnumber 添加了规则: LENGTH
2025-07-17 12:33:43.301 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnimsi.format - FORMAT
2025-07-17 12:33:43.301 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnImsi, 规则ID=common.mcnImsi.format, 规则名称=格式校验
2025-07-17 12:33:43.301 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnImsi 添加了规则: FORMAT
2025-07-17 12:33:43.301 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnimsi.length - LENGTH
2025-07-17 12:33:43.301 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnImsi, 规则ID=common.mcnImsi.length, 规则名称=长度校验
2025-07-17 12:33:43.301 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnImsi 添加了规则: LENGTH
2025-07-17 12:33:43.301 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.businessstate.enum - ENUM
2025-07-17 12:33:43.301 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=businessState, 规则ID=common.businessState.enum, 规则名称=枚举值校验
2025-07-17 12:33:43.301 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 businessState 添加了规则: ENUM
2025-07-17 12:33:43.301 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.numstate.format - FORMAT
2025-07-17 12:33:43.301 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=Numstate, 规则ID=common.Numstate.format, 规则名称=格式校验
2025-07-17 12:33:43.301 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 Numstate 添加了规则: FORMAT
2025-07-17 12:33:43.301 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.locationid.length - LENGTH
2025-07-17 12:33:43.301 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=Locationid, 规则ID=common.Locationid.length, 规则名称=长度校验
2025-07-17 12:33:43.301 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 Locationid 添加了规则: LENGTH
2025-07-17 12:33:43.301 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.bossprovinceid.format - FORMAT
2025-07-17 12:33:43.301 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=BossProvinceid, 规则ID=common.BossProvinceid.format, 规则名称=格式校验
2025-07-17 12:33:43.301 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 BossProvinceid 添加了规则: FORMAT
2025-07-17 12:33:43.301 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.bossprovinceid.range - RANGE
2025-07-17 12:33:43.301 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=BossProvinceid, 规则ID=common.BossProvinceid.range, 规则名称=范围校验
2025-07-17 12:33:43.301 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 BossProvinceid 添加了规则: RANGE
2025-07-17 12:33:43.301 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.openingtime.format - FORMAT
2025-07-17 12:33:43.301 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=openingtime, 规则ID=common.openingtime.format, 规则名称=格式校验
2025-07-17 12:33:43.302 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 openingtime 添加了规则: FORMAT
2025-07-17 12:33:43.302 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24207, 字段数量=8
2025-07-17 12:33:43.302 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 注册了 8 个字段的元数据
2025-07-17 12:33:43.302 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [Numstate, openingtime, mcnImsi, businessState, BossProvinceid, phonenumber, Locationid, mcnnumber] -> VGOP1-R2-10-24207
2025-07-17 12:33:43.302 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 注册了字段到接口的映射关系
2025-07-17 12:33:43.302 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24207 加载了 12 个字段校验规则
2025-07-17 12:33:43.302 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-11-24101 的校验规则...
2025-07-17 12:33:43.302 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.phonenum.format - FORMAT
2025-07-17 12:33:43.302 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=phonenum, 规则ID=common.phonenum.format, 规则名称=格式校验
2025-07-17 12:33:43.302 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 phonenum 添加了规则: FORMAT
2025-07-17 12:33:43.302 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.phonenum.range - RANGE
2025-07-17 12:33:43.302 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=phonenum, 规则ID=common.phonenum.range, 规则名称=范围校验
2025-07-17 12:33:43.302 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 phonenum 添加了规则: RANGE
2025-07-17 12:33:43.302 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnnum.format - FORMAT
2025-07-17 12:33:43.302 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnnum, 规则ID=common.mcnnum.format, 规则名称=格式校验
2025-07-17 12:33:43.302 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnnum 添加了规则: FORMAT
2025-07-17 12:33:43.302 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnnum.range - RANGE
2025-07-17 12:33:43.302 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnnum, 规则ID=common.mcnnum.range, 规则名称=范围校验
2025-07-17 12:33:43.302 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnnum 添加了规则: RANGE
2025-07-17 12:33:43.302 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.appactivenum.format - FORMAT
2025-07-17 12:33:43.302 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=appactivenum, 规则ID=common.appactivenum.format, 规则名称=格式校验
2025-07-17 12:33:43.302 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 appactivenum 添加了规则: FORMAT
2025-07-17 12:33:43.302 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.appactivenum.range - RANGE
2025-07-17 12:33:43.302 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=appactivenum, 规则ID=common.appactivenum.range, 规则名称=范围校验
2025-07-17 12:33:43.302 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 appactivenum 添加了规则: RANGE
2025-07-17 12:33:43.304 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnactivenum.format - FORMAT
2025-07-17 12:33:43.304 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnactivenum, 规则ID=common.mcnactivenum.format, 规则名称=格式校验
2025-07-17 12:33:43.304 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnactivenum 添加了规则: FORMAT
2025-07-17 12:33:43.304 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnactivenum.range - RANGE
2025-07-17 12:33:43.304 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnactivenum, 规则ID=common.mcnactivenum.range, 规则名称=范围校验
2025-07-17 12:33:43.304 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnactivenum 添加了规则: RANGE
2025-07-17 12:33:43.304 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.paynum.format - FORMAT
2025-07-17 12:33:43.304 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=paynum, 规则ID=common.paynum.format, 规则名称=格式校验
2025-07-17 12:33:43.304 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 paynum 添加了规则: FORMAT
2025-07-17 12:33:43.304 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.paynum.range - RANGE
2025-07-17 12:33:43.304 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=paynum, 规则ID=common.paynum.range, 规则名称=范围校验
2025-07-17 12:33:43.304 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 paynum 添加了规则: RANGE
2025-07-17 12:33:43.304 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.feenum.format - FORMAT
2025-07-17 12:33:43.304 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=feenum, 规则ID=common.feenum.format, 规则名称=格式校验
2025-07-17 12:33:43.304 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 feenum 添加了规则: FORMAT
2025-07-17 12:33:43.304 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.feenum.range - RANGE
2025-07-17 12:33:43.304 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=feenum, 规则ID=common.feenum.range, 规则名称=范围校验
2025-07-17 12:33:43.304 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 feenum 添加了规则: RANGE
2025-07-17 12:33:43.304 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secphonenum.format - FORMAT
2025-07-17 12:33:43.304 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secphonenum, 规则ID=common.secphonenum.format, 规则名称=格式校验
2025-07-17 12:33:43.304 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secphonenum 添加了规则: FORMAT
2025-07-17 12:33:43.304 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secphonenum.range - RANGE
2025-07-17 12:33:43.304 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secphonenum, 规则ID=common.secphonenum.range, 规则名称=范围校验
2025-07-17 12:33:43.304 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secphonenum 添加了规则: RANGE
2025-07-17 12:33:43.304 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secmcnnum.format - FORMAT
2025-07-17 12:33:43.304 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secmcnnum, 规则ID=common.secmcnnum.format, 规则名称=格式校验
2025-07-17 12:33:43.305 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secmcnnum 添加了规则: FORMAT
2025-07-17 12:33:43.305 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secmcnnum.range - RANGE
2025-07-17 12:33:43.305 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secmcnnum, 规则ID=common.secmcnnum.range, 规则名称=范围校验
2025-07-17 12:33:43.305 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secmcnnum 添加了规则: RANGE
2025-07-17 12:33:43.305 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.amcnnum.format - FORMAT
2025-07-17 12:33:43.305 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=amcnnum, 规则ID=common.amcnnum.format, 规则名称=格式校验
2025-07-17 12:33:43.305 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 amcnnum 添加了规则: FORMAT
2025-07-17 12:33:43.305 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.amcnnum.range - RANGE
2025-07-17 12:33:43.305 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=amcnnum, 规则ID=common.amcnnum.range, 规则名称=范围校验
2025-07-17 12:33:43.305 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 amcnnum 添加了规则: RANGE
2025-07-17 12:33:43.305 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-11-24101, 字段数量=9
2025-07-17 12:33:43.305 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 注册了 9 个字段的元数据
2025-07-17 12:33:43.305 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [appactivenum, paynum, mcnnum, secphonenum, phonenum, mcnactivenum, secmcnnum, amcnnum, feenum] -> VGOP1-R2-11-24101
2025-07-17 12:33:43.305 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 注册了字段到接口的映射关系
2025-07-17 12:33:43.305 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-11-24101 加载了 18 个字段校验规则
2025-07-17 12:33:43.305 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-13-24301 的校验规则...
2025-07-17 12:33:43.305 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servid.format - FORMAT
2025-07-17 12:33:43.305 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServID, 规则ID=common.ServID.format, 规则名称=格式校验
2025-07-17 12:33:43.305 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServID 添加了规则: FORMAT
2025-07-17 12:33:43.305 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servid.length - LENGTH
2025-07-17 12:33:43.305 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServID, 规则ID=common.ServID.length, 规则名称=长度校验
2025-07-17 12:33:43.305 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServID 添加了规则: LENGTH
2025-07-17 12:33:43.305 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servid.standard_format - LENGTH
2025-07-17 12:33:43.305 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServID, 规则ID=common.ServID.length, 规则名称=长度校验
2025-07-17 12:33:43.305 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServID 添加了规则: LENGTH
2025-07-17 12:33:43.306 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servname.length - LENGTH
2025-07-17 12:33:43.306 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServName, 规则ID=common.ServName.length, 规则名称=长度校验
2025-07-17 12:33:43.306 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServName 添加了规则: LENGTH
2025-07-17 12:33:43.306 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servname.content - FORMAT
2025-07-17 12:33:43.306 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServName, 规则ID=common.ServName.format, 规则名称=格式校验
2025-07-17 12:33:43.306 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServName 添加了规则: FORMAT
2025-07-17 12:33:43.306 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-13-24301, 字段数量=2
2025-07-17 12:33:43.306 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 注册了 2 个字段的元数据
2025-07-17 12:33:43.306 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [ServName, ServID] -> VGOP1-R2-13-24301
2025-07-17 12:33:43.306 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 注册了字段到接口的映射关系
2025-07-17 12:33:43.306 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-13-24301 加载了 5 个字段校验规则
2025-07-17 12:33:43.306 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-13-24302 的校验规则...
2025-07-17 12:33:43.306 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelcode.format - FORMAT
2025-07-17 12:33:43.306 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelcode, 规则ID=common.channelcode.format, 规则名称=格式校验
2025-07-17 12:33:43.306 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelcode 添加了规则: FORMAT
2025-07-17 12:33:43.306 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelcode.length - LENGTH
2025-07-17 12:33:43.306 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelcode, 规则ID=common.channelcode.length, 规则名称=长度校验
2025-07-17 12:33:43.306 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelcode 添加了规则: LENGTH
2025-07-17 12:33:43.306 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelcode.range - RANGE
2025-07-17 12:33:43.307 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelcode, 规则ID=common.channelcode.range, 规则名称=范围校验
2025-07-17 12:33:43.307 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelcode 添加了规则: RANGE
2025-07-17 12:33:43.307 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelname.length - LENGTH
2025-07-17 12:33:43.307 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelname, 规则ID=common.channelname.length, 规则名称=长度校验
2025-07-17 12:33:43.307 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelname 添加了规则: LENGTH
2025-07-17 12:33:43.307 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelname.content - FORMAT
2025-07-17 12:33:43.307 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelname, 规则ID=common.channelname.format, 规则名称=格式校验
2025-07-17 12:33:43.307 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelname 添加了规则: FORMAT
2025-07-17 12:33:43.307 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-13-24302, 字段数量=2
2025-07-17 12:33:43.307 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 注册了 2 个字段的元数据
2025-07-17 12:33:43.307 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [channelname, channelcode] -> VGOP1-R2-13-24302
2025-07-17 12:33:43.307 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 注册了字段到接口的映射关系
2025-07-17 12:33:43.307 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-13-24302 加载了 5 个字段校验规则
2025-07-17 12:33:43.307 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-13-24303 的校验规则...
2025-07-17 12:33:43.307 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdown.format - FORMAT
2025-07-17 12:33:43.307 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdown, 规则ID=common.shutdown.format, 规则名称=格式校验
2025-07-17 12:33:43.307 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdown 添加了规则: FORMAT
2025-07-17 12:33:43.307 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdown.length - LENGTH
2025-07-17 12:33:43.307 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdown, 规则ID=common.shutdown.length, 规则名称=长度校验
2025-07-17 12:33:43.307 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdown 添加了规则: LENGTH
2025-07-17 12:33:43.307 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdown.range - RANGE
2025-07-17 12:33:43.307 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdown, 规则ID=common.shutdown.range, 规则名称=范围校验
2025-07-17 12:33:43.307 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdown 添加了规则: RANGE
2025-07-17 12:33:43.307 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdownname.length - LENGTH
2025-07-17 12:33:43.307 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdownname, 规则ID=common.shutdownname.length, 规则名称=长度校验
2025-07-17 12:33:43.307 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdownname 添加了规则: LENGTH
2025-07-17 12:33:43.307 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdownname.content - FORMAT
2025-07-17 12:33:43.308 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdownname, 规则ID=common.shutdownname.format, 规则名称=格式校验
2025-07-17 12:33:43.308 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdownname 添加了规则: FORMAT
2025-07-17 12:33:43.308 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-13-24303, 字段数量=2
2025-07-17 12:33:43.308 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 注册了 2 个字段的元数据
2025-07-17 12:33:43.308 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [shutdownname, shutdown] -> VGOP1-R2-13-24303
2025-07-17 12:33:43.308 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 注册了字段到接口的映射关系
2025-07-17 12:33:43.308 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-13-24303 加载了 5 个字段校验规则
2025-07-17 12:33:43.308 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 校验规则加载完成，共加载 100 个规则
2025-07-17 12:33:56.998 [Thread-543] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-17 12:33:57.640 [Thread-543] INFO  o.s.s.quartz.SchedulerFactoryBean [||||] [] - Shutting down Quartz Scheduler
2025-07-17 12:33:57.640 [Thread-543] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-17 12:33:57.640 [Thread-543] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-17 12:33:57.640 [Thread-543] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-17 12:33:57.642 [Thread-543] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-4} closing ...
2025-07-17 12:33:57.699 [Thread-543] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-4} closed
2025-07-17 12:33:57.700 [Thread-543] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-3} closing ...
2025-07-17 12:33:57.756 [Thread-543] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-3} closed
2025-07-17 12:33:58.215 [restartedMain] INFO  c.v.service.VgopServiceApplication [||||] [] - Starting VgopServiceApplication using Java 1.8.0_452 on ziber with PID 25692 (C:\workspaces\hdh\vgop-vli\vgop-service\target\classes started by galil in C:\workspaces\hdh\vgop-vli\vgop-service)
2025-07-17 12:33:58.215 [restartedMain] DEBUG c.v.service.VgopServiceApplication [||||] [] - Running with Spring Boot v2.5.5, Spring v5.3.19
2025-07-17 12:33:58.216 [restartedMain] INFO  c.v.service.VgopServiceApplication [||||] [] - The following profiles are active: dev
2025-07-17 12:33:58.836 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\DataExportMapper.class]
2025-07-17 12:33:58.836 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\RevisionMapper.class]
2025-07-17 12:33:58.836 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\ValidationAlertsMapper.class]
2025-07-17 12:33:58.836 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\primary\RevisionTimesMapper.class]
2025-07-17 12:33:58.836 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\secondary\TaskExecutionMapper.class]
2025-07-17 12:33:58.836 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'dataExportMapper' and 'com.vgop.service.dao.DataExportMapper' mapperInterface
2025-07-17 12:33:58.837 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'revisionMapper' and 'com.vgop.service.dao.RevisionMapper' mapperInterface
2025-07-17 12:33:58.837 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'validationAlertsMapper' and 'com.vgop.service.dao.ValidationAlertsMapper' mapperInterface
2025-07-17 12:33:58.837 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'revisionTimesMapper' and 'com.vgop.service.dao.primary.RevisionTimesMapper' mapperInterface
2025-07-17 12:33:58.837 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'taskExecutionMapper' and 'com.vgop.service.dao.secondary.TaskExecutionMapper' mapperInterface
2025-07-17 12:33:58.867 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [||||] [] - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-17 12:33:58.969 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer [||||] [] - Tomcat initialized with port(s): 8080 (http)
2025-07-17 12:33:58.970 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol [||||] [] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-17 12:33:58.970 [restartedMain] INFO  o.a.catalina.core.StandardService [||||] [] - Starting service [Tomcat]
2025-07-17 12:33:58.970 [restartedMain] INFO  o.a.catalina.core.StandardEngine [||||] [] - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-17 12:33:59.007 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/vgop] [||||] [] - Initializing Spring embedded WebApplicationContext
2025-07-17 12:33:59.007 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext [||||] [] - Root WebApplicationContext: initialization completed in 789 ms
2025-07-17 12:33:59.077 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - ValidationEngine已初始化，等待规则配置...
2025-07-17 12:34:00.131 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\mapper\DataExportMapper.xml]'
2025-07-17 12:34:00.140 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\mapper\RevisionMapper.xml]'
2025-07-17 12:34:00.149 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\mapper\TaskExecutionMapper.xml]'
2025-07-17 12:34:00.160 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\mapper\ValidationAlertsMapper.xml]'
2025-07-17 12:34:00.294 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - === 开始测试数据库连接 ===
2025-07-17 12:34:00.294 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 测试主数据源连接...
2025-07-17 12:34:00.294 [restartedMain] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - testWhileIdle is true, validationQuery not set
2025-07-17 12:34:00.296 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-5} inited
2025-07-17 12:34:00.871 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 主数据源连接成功: {databaseProductVersion=12.10.FC4G1AEE, databaseProductName=GBase Server, driverVersion=4.10.JC4G1N999, name=主数据源, driverName=GBase JDBC Driver for GBase Server, userName=ismp, url=jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;}
2025-07-17 12:34:00.872 [restartedMain] INFO  com.vgop.service.util.DatabaseUtil [||||] [] - 检测到数据库产品: GBase Server, URL: jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;
2025-07-17 12:34:00.872 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 检测到主数据库类型: gbase
2025-07-17 12:34:00.872 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 是否支持UNLOAD命令: true
2025-07-17 12:34:00.872 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 测试次数据源连接...
2025-07-17 12:34:00.873 [restartedMain] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - testWhileIdle is true, validationQuery not set
2025-07-17 12:34:00.875 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-6} inited
2025-07-17 12:34:01.438 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 次数据源连接成功: {databaseProductVersion=12.10.FC4G1AEE, databaseProductName=GBase Server, driverVersion=4.10.JC4G1N999, name=次数据源, driverName=GBase JDBC Driver for GBase Server, userName=ismp, url=jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;}
2025-07-17 12:34:01.438 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - === 数据库连接测试完成 ===
2025-07-17 12:34:01.551 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory [||||] [] - Using default implementation for ThreadExecutor
2025-07-17 12:34:01.552 [restartedMain] INFO  o.quartz.core.SchedulerSignalerImpl [||||] [] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-17 12:34:01.552 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - Quartz Scheduler v.2.3.2 created.
2025-07-17 12:34:01.552 [restartedMain] INFO  org.quartz.simpl.RAMJobStore [||||] [] - RAMJobStore initialized.
2025-07-17 12:34:01.552 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-17 12:34:01.552 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory [||||] [] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-17 12:34:01.552 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory [||||] [] - Quartz scheduler version: 2.3.2
2025-07-17 12:34:01.552 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2b2d1f6e
2025-07-17 12:34:01.592 [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver [||||] [] - Exposing 15 endpoint(s) beneath base path '/actuator'
2025-07-17 12:34:01.624 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:34:01.624 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库产品名称: GBase Server, 版本: 12.10.FC4G1AEE
2025-07-17 12:34:01.624 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 检测到数据库类型: GBase Server
2025-07-17 12:34:01.624 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 配置GBase数据库字符编码设置
2025-07-17 12:34:01.624 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]
2025-07-17 12:34:01.624 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:34:01.944 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Looking up default SQLErrorCodes for DataSource [com.alibaba.druid.pool.DruidDataSource@6c29d5bc]
2025-07-17 12:34:01.944 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:34:01.945 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - SQL error codes for 'GBase Server' not found
2025-07-17 12:34:01.945 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Caching SQL error codes for DataSource [com.alibaba.druid.pool.DruidDataSource@6c29d5bc]: database product name is 'GBase Server'
2025-07-17 12:34:01.945 [restartedMain] DEBUG o.s.j.s.SQLErrorCodeSQLExceptionTranslator [||||] [] - Unable to translate SQLException with Error code '-728', will now try the fallback translator
2025-07-17 12:34:01.945 [restartedMain] DEBUG o.s.j.s.SQLStateSQLExceptionTranslator [||||] [] - Extracted SQL state class 'IX' from value 'IX000'
2025-07-17 12:34:01.945 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 查询GBase数据库字符编码信息失败: StatementCallback; uncategorized SQLException for SQL [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]; SQL state [IX000]; error code [-728]; Unknown first argument of dbinfo(dblocale).; nested exception is java.sql.SQLException: Unknown first argument of dbinfo(dblocale).
2025-07-17 12:34:01.945 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - GBase数据库字符编码配置完成，依赖JDBC URL参数: DB_LOCALE, NEWCODESET
2025-07-17 12:34:01.946 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dbhostname') FROM systables WHERE tabid = 1]
2025-07-17 12:34:01.946 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:34:02.506 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库主机名: eb33207
2025-07-17 12:34:02.507 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT LENGTH('测试中文字符') FROM systables WHERE tabid = 1]
2025-07-17 12:34:02.507 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:34:02.832 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 字符编码兼容性测试通过，中文字符长度: 12
2025-07-17 12:34:02.833 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 已配置主数据源字符编码安全的JdbcTemplate
2025-07-17 12:34:02.833 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:34:02.834 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库产品名称: GBase Server, 版本: 12.10.FC4G1AEE
2025-07-17 12:34:02.834 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 检测到数据库类型: GBase Server
2025-07-17 12:34:02.834 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 配置GBase数据库字符编码设置
2025-07-17 12:34:02.834 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]
2025-07-17 12:34:02.834 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:34:03.183 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Looking up default SQLErrorCodes for DataSource [com.alibaba.druid.pool.DruidDataSource@341bd6fe]
2025-07-17 12:34:03.184 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:34:03.184 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - SQL error codes for 'GBase Server' not found
2025-07-17 12:34:03.184 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Caching SQL error codes for DataSource [com.alibaba.druid.pool.DruidDataSource@341bd6fe]: database product name is 'GBase Server'
2025-07-17 12:34:03.184 [restartedMain] DEBUG o.s.j.s.SQLErrorCodeSQLExceptionTranslator [||||] [] - Unable to translate SQLException with Error code '-728', will now try the fallback translator
2025-07-17 12:34:03.184 [restartedMain] DEBUG o.s.j.s.SQLStateSQLExceptionTranslator [||||] [] - Extracted SQL state class 'IX' from value 'IX000'
2025-07-17 12:34:03.184 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 查询GBase数据库字符编码信息失败: StatementCallback; uncategorized SQLException for SQL [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]; SQL state [IX000]; error code [-728]; Unknown first argument of dbinfo(dblocale).; nested exception is java.sql.SQLException: Unknown first argument of dbinfo(dblocale).
2025-07-17 12:34:03.184 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - GBase数据库字符编码配置完成，依赖JDBC URL参数: DB_LOCALE, NEWCODESET
2025-07-17 12:34:03.184 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dbhostname') FROM systables WHERE tabid = 1]
2025-07-17 12:34:03.184 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:34:03.467 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库主机名: eb33207
2025-07-17 12:34:03.468 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT LENGTH('测试中文字符') FROM systables WHERE tabid = 1]
2025-07-17 12:34:03.468 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:34:03.777 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 字符编码兼容性测试通过，中文字符长度: 12
2025-07-17 12:34:03.777 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 已配置次数据源字符编码安全的JdbcTemplate
2025-07-17 12:34:03.808 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer [||||] [] - LiveReload server is running on port 35729
2025-07-17 12:34:03.898 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol [||||] [] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-17 12:34:03.900 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer [||||] [] - Tomcat started on port(s): 8080 (http) with context path '/vgop'
2025-07-17 12:34:04.109 [restartedMain] INFO  o.s.s.quartz.SchedulerFactoryBean [||||] [] - Starting Quartz Scheduler now
2025-07-17 12:34:04.110 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-17 12:34:04.119 [restartedMain] INFO  c.v.service.VgopServiceApplication [||||] [] - Started VgopServiceApplication in 6.09 seconds (JVM running for 1233.818)
2025-07-17 12:34:04.121 [restartedMain] INFO  c.v.s.s.ConfigValidationService [||||] [] - 开始验证应用配置...
2025-07-17 12:34:04.121 [restartedMain] INFO  c.v.s.s.ConfigValidationService [||||] [] - 配置验证通过
2025-07-17 12:34:04.122 [restartedMain] INFO  c.v.s.service.DirectoryInitService [||||] [] - 开始初始化目录结构...
2025-07-17 12:34:04.122 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 数据导出根目录 - ./VGOPdata/datafile/
2025-07-17 12:34:04.122 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日志根目录 - ./logs/
2025-07-17 12:34:04.122 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 备份根目录 - ./data/backup/
2025-07-17 12:34:04.122 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 告警文件目录 - ./data/alerts/
2025-07-17 12:34:04.124 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250714 - ./VGOPdata/datafile//20250714/day
2025-07-17 12:34:04.124 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250714 - ./VGOPdata/datafile//20250714/month
2025-07-17 12:34:04.124 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250715 - ./VGOPdata/datafile//20250715/day
2025-07-17 12:34:04.124 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250715 - ./VGOPdata/datafile//20250715/month
2025-07-17 12:34:04.124 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250716 - ./VGOPdata/datafile//20250716/day
2025-07-17 12:34:04.124 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250716 - ./VGOPdata/datafile//20250716/month
2025-07-17 12:34:04.125 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250717 - ./VGOPdata/datafile//20250717/day
2025-07-17 12:34:04.125 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250717 - ./VGOPdata/datafile//20250717/month
2025-07-17 12:34:04.125 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250718 - ./VGOPdata/datafile//20250718/day
2025-07-17 12:34:04.125 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250718 - ./VGOPdata/datafile//20250718/month
2025-07-17 12:34:04.127 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250719 - ./VGOPdata/datafile//20250719/day
2025-07-17 12:34:04.127 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250719 - ./VGOPdata/datafile//20250719/month
2025-07-17 12:34:04.127 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月度数据目录 - 202507 - ./VGOPdata/datafile//202507
2025-07-17 12:34:04.127 [restartedMain] INFO  c.v.s.service.DirectoryInitService [||||] [] - 目录结构初始化完成
2025-07-17 12:34:04.127 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener [||||] [] - Condition evaluation unchanged
2025-07-17 12:34:04.128 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 开始从配置文件加载校验规则...
2025-07-17 12:34:04.128 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24201 的校验规则...
2025-07-17 12:34:04.128 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phonenumber.format - FORMAT
2025-07-17 12:34:04.128 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-17 12:34:04.128 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phonenumber 添加了规则: FORMAT
2025-07-17 12:34:04.128 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phonenumber.length - LENGTH
2025-07-17 12:34:04.130 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-17 12:34:04.130 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phonenumber 添加了规则: LENGTH
2025-07-17 12:34:04.130 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phonestate.enum - ENUM
2025-07-17 12:34:04.130 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phonestate, 规则ID=common.phonestate.enum, 规则名称=枚举值校验
2025-07-17 12:34:04.130 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phonestate 添加了规则: ENUM
2025-07-17 12:34:04.130 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phoneimsi.length - LENGTH
2025-07-17 12:34:04.130 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phoneimsi, 规则ID=common.phoneimsi.length, 规则名称=长度校验
2025-07-17 12:34:04.130 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phoneimsi 添加了规则: LENGTH
2025-07-17 12:34:04.130 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phoneimei.length - LENGTH
2025-07-17 12:34:04.130 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phoneimei, 规则ID=common.phoneimei.length, 规则名称=长度校验
2025-07-17 12:34:04.130 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phoneimei 添加了规则: LENGTH
2025-07-17 12:34:04.130 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.locationid.length - LENGTH
2025-07-17 12:34:04.130 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=locationid, 规则ID=common.locationid.length, 规则名称=长度校验
2025-07-17 12:34:04.130 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 locationid 添加了规则: LENGTH
2025-07-17 12:34:04.130 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.provinceid.length - LENGTH
2025-07-17 12:34:04.130 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=provinceid, 规则ID=common.provinceid.length, 规则名称=长度校验
2025-07-17 12:34:04.130 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 provinceid 添加了规则: LENGTH
2025-07-17 12:34:04.130 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.openingtime.format - FORMAT
2025-07-17 12:34:04.130 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=openingtime, 规则ID=common.openingtime.format, 规则名称=格式校验
2025-07-17 12:34:04.130 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 openingtime 添加了规则: FORMAT
2025-07-17 12:34:04.130 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.optime.format - FORMAT
2025-07-17 12:34:04.130 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=Optime, 规则ID=common.Optime.format, 规则名称=格式校验
2025-07-17 12:34:04.130 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 Optime 添加了规则: FORMAT
2025-07-17 12:34:04.130 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.sex.enum - ENUM
2025-07-17 12:34:04.130 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=sex, 规则ID=common.sex.enum, 规则名称=枚举值校验
2025-07-17 12:34:04.130 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 sex 添加了规则: ENUM
2025-07-17 12:34:04.131 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24201, 字段数量=9
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 注册了 9 个字段的元数据
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [phonestate, Optime, phoneimei, openingtime, locationid, sex, phonenumber, phoneimsi, provinceid] -> VGOP1-R2-10-24201
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 注册了字段到接口的映射关系
2025-07-17 12:34:04.131 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24201 加载了 10 个字段校验规则
2025-07-17 12:34:04.131 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24202 的校验规则...
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnumber.format - FORMAT
2025-07-17 12:34:04.131 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnumber 添加了规则: FORMAT
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnumber.length - LENGTH
2025-07-17 12:34:04.131 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnumber, 规则ID=common.mcnnumber.length, 规则名称=长度校验
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnumber 添加了规则: LENGTH
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.phonenumber.format - FORMAT
2025-07-17 12:34:04.131 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 phonenumber 添加了规则: FORMAT
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.phonenumber.length - LENGTH
2025-07-17 12:34:04.131 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 phonenumber 添加了规则: LENGTH
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.business.enum - ENUM
2025-07-17 12:34:04.131 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=business, 规则ID=common.business.enum, 规则名称=枚举值校验
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 business 添加了规则: ENUM
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.shutdown.length - LENGTH
2025-07-17 12:34:04.131 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=shutdown, 规则ID=common.shutdown.length, 规则名称=长度校验
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 shutdown 添加了规则: LENGTH
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnimsi.length - LENGTH
2025-07-17 12:34:04.131 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnimsi, 规则ID=common.mcnimsi.length, 规则名称=长度校验
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnimsi 添加了规则: LENGTH
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnimsi.format - FORMAT
2025-07-17 12:34:04.131 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnimsi, 规则ID=common.mcnimsi.format, 规则名称=格式校验
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnimsi 添加了规则: FORMAT
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnlocationid.length - LENGTH
2025-07-17 12:34:04.131 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnlocationid, 规则ID=common.mcnlocationid.length, 规则名称=长度校验
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnlocationid 添加了规则: LENGTH
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.numstate.length - LENGTH
2025-07-17 12:34:04.131 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=numstate, 规则ID=common.numstate.length, 规则名称=长度校验
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 numstate 添加了规则: LENGTH
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnature.enum - ENUM
2025-07-17 12:34:04.131 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnature, 规则ID=common.mcnnature.enum, 规则名称=枚举值校验
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnature 添加了规则: ENUM
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnum.enum - ENUM
2025-07-17 12:34:04.131 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnum, 规则ID=common.mcnnum.enum, 规则名称=枚举值校验
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnum 添加了规则: ENUM
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnum.length - LENGTH
2025-07-17 12:34:04.131 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnum, 规则ID=common.mcnnum.length, 规则名称=长度校验
2025-07-17 12:34:04.131 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnum 添加了规则: LENGTH
2025-07-17 12:34:04.132 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.channel.length - LENGTH
2025-07-17 12:34:04.132 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=channel, 规则ID=common.channel.length, 规则名称=长度校验
2025-07-17 12:34:04.132 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 channel 添加了规则: LENGTH
2025-07-17 12:34:04.132 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mj.enum - ENUM
2025-07-17 12:34:04.132 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mj, 规则ID=common.mj.enum, 规则名称=枚举值校验
2025-07-17 12:34:04.132 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mj 添加了规则: ENUM
2025-07-17 12:34:04.132 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.openingtime.format - FORMAT
2025-07-17 12:34:04.132 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=openingtime, 规则ID=common.openingtime.format, 规则名称=格式校验
2025-07-17 12:34:04.132 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 openingtime 添加了规则: FORMAT
2025-07-17 12:34:04.132 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.optime.format - FORMAT
2025-07-17 12:34:04.132 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=Optime, 规则ID=common.Optime.format, 规则名称=格式校验
2025-07-17 12:34:04.132 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 Optime 添加了规则: FORMAT
2025-07-17 12:34:04.132 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcimsitime.format - FORMAT
2025-07-17 12:34:04.132 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcimsitime, 规则ID=common.mcimsitime.format, 规则名称=格式校验
2025-07-17 12:34:04.132 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcimsitime 添加了规则: FORMAT
2025-07-17 12:34:04.132 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.usertype.length - LENGTH
2025-07-17 12:34:04.132 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=usertype, 规则ID=common.usertype.length, 规则名称=长度校验
2025-07-17 12:34:04.132 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 usertype 添加了规则: LENGTH
2025-07-17 12:34:04.132 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.begintime.format - FORMAT
2025-07-17 12:34:04.132 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=Begintime, 规则ID=common.Begintime.format, 规则名称=格式校验
2025-07-17 12:34:04.132 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 Begintime 添加了规则: FORMAT
2025-07-17 12:34:04.132 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.endtime.format - FORMAT
2025-07-17 12:34:04.132 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=Endtime, 规则ID=common.Endtime.format, 规则名称=格式校验
2025-07-17 12:34:04.132 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 Endtime 添加了规则: FORMAT
2025-07-17 12:34:04.132 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.servid.length - LENGTH
2025-07-17 12:34:04.132 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=ServID, 规则ID=common.ServID.length, 规则名称=长度校验
2025-07-17 12:34:04.132 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 ServID 添加了规则: LENGTH
2025-07-17 12:34:04.132 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24202, 字段数量=18
2025-07-17 12:34:04.132 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 注册了 18 个字段的元数据
2025-07-17 12:34:04.133 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [mcnlocationid, Optime, business, mcnimsi, mcimsitime, phonenumber, mcnnum, channel, usertype, numstate, Endtime, openingtime, ServID, mcnnature, Begintime, mcnnumber, mj, shutdown] -> VGOP1-R2-10-24202
2025-07-17 12:34:04.133 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 注册了字段到接口的映射关系
2025-07-17 12:34:04.133 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24202 加载了 22 个字段校验规则
2025-07-17 12:34:04.133 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24203 的校验规则...
2025-07-17 12:34:04.133 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.account_id.length - LENGTH
2025-07-17 12:34:04.133 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=account_id, 规则ID=common.account_id.length, 规则名称=长度校验
2025-07-17 12:34:04.133 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 account_id 添加了规则: LENGTH
2025-07-17 12:34:04.133 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.phonenumber.format - FORMAT
2025-07-17 12:34:04.133 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-17 12:34:04.133 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 phonenumber 添加了规则: FORMAT
2025-07-17 12:34:04.133 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.phonenumber.length - LENGTH
2025-07-17 12:34:04.133 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-17 12:34:04.133 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 phonenumber 添加了规则: LENGTH
2025-07-17 12:34:04.133 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.type.enum - ENUM
2025-07-17 12:34:04.133 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=type, 规则ID=common.type.enum, 规则名称=枚举值校验
2025-07-17 12:34:04.133 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 type 添加了规则: ENUM
2025-07-17 12:34:04.133 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.optime.format - FORMAT
2025-07-17 12:34:04.133 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=optime, 规则ID=common.optime.format, 规则名称=格式校验
2025-07-17 12:34:04.133 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 optime 添加了规则: FORMAT
2025-07-17 12:34:04.133 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.version.length - LENGTH
2025-07-17 12:34:04.133 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=version, 规则ID=common.version.length, 规则名称=长度校验
2025-07-17 12:34:04.133 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 version 添加了规则: LENGTH
2025-07-17 12:34:04.134 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24203, 字段数量=5
2025-07-17 12:34:04.134 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 注册了 5 个字段的元数据
2025-07-17 12:34:04.134 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [account_id, optime, phonenumber, type, version] -> VGOP1-R2-10-24203
2025-07-17 12:34:04.134 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 注册了字段到接口的映射关系
2025-07-17 12:34:04.134 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24203 加载了 6 个字段校验规则
2025-07-17 12:34:04.134 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24205 的校验规则...
2025-07-17 12:34:04.134 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.calltype.enum - ENUM
2025-07-17 12:34:04.134 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=callType, 规则ID=common.callType.enum, 规则名称=枚举值校验
2025-07-17 12:34:04.134 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 callType 添加了规则: ENUM
2025-07-17 12:34:04.134 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callingpartynumber.format - FORMAT
2025-07-17 12:34:04.134 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=callingPartyNumber, 规则ID=common.callingPartyNumber.format, 规则名称=格式校验
2025-07-17 12:34:04.134 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 callingPartyNumber 添加了规则: FORMAT
2025-07-17 12:34:04.134 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callingpartynumber.length - LENGTH
2025-07-17 12:34:04.134 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=callingPartyNumber, 规则ID=common.callingPartyNumber.length, 规则名称=长度校验
2025-07-17 12:34:04.134 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 callingPartyNumber 添加了规则: LENGTH
2025-07-17 12:34:04.134 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.calledpartynumber.format - FORMAT
2025-07-17 12:34:04.134 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=calledPartyNumber, 规则ID=common.calledPartyNumber.format, 规则名称=格式校验
2025-07-17 12:34:04.134 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 calledPartyNumber 添加了规则: FORMAT
2025-07-17 12:34:04.134 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.calledpartynumber.length - LENGTH
2025-07-17 12:34:04.134 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=calledPartyNumber, 规则ID=common.calledPartyNumber.length, 规则名称=长度校验
2025-07-17 12:34:04.134 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 calledPartyNumber 添加了规则: LENGTH
2025-07-17 12:34:04.134 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.mcnnumber.format - FORMAT
2025-07-17 12:34:04.134 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-17 12:34:04.134 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 mcnnumber 添加了规则: FORMAT
2025-07-17 12:34:04.134 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callbegintime.format - FORMAT
2025-07-17 12:34:04.134 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallBeginTime, 规则ID=common.CallBeginTime.format, 规则名称=格式校验
2025-07-17 12:34:04.134 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallBeginTime 添加了规则: FORMAT
2025-07-17 12:34:04.135 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callendtime.format - FORMAT
2025-07-17 12:34:04.135 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallEndTime, 规则ID=common.CallEndTime.format, 规则名称=格式校验
2025-07-17 12:34:04.135 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallEndTime 添加了规则: FORMAT
2025-07-17 12:34:04.135 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callduration.format - FORMAT
2025-07-17 12:34:04.135 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallDuration, 规则ID=common.CallDuration.format, 规则名称=格式校验
2025-07-17 12:34:04.135 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallDuration 添加了规则: FORMAT
2025-07-17 12:34:04.135 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callduration.range - RANGE
2025-07-17 12:34:04.135 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallDuration, 规则ID=common.CallDuration.range, 规则名称=范围校验
2025-07-17 12:34:04.135 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallDuration 添加了规则: RANGE
2025-07-17 12:34:04.135 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24205, 字段数量=7
2025-07-17 12:34:04.135 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 注册了 7 个字段的元数据
2025-07-17 12:34:04.135 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [CallEndTime, callingPartyNumber, calledPartyNumber, CallBeginTime, mcnnumber, CallDuration, callType] -> VGOP1-R2-10-24205
2025-07-17 12:34:04.135 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 注册了字段到接口的映射关系
2025-07-17 12:34:04.135 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24205 加载了 10 个字段校验规则
2025-07-17 12:34:04.135 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24206 的校验规则...
2025-07-17 12:34:04.135 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.chargetype.enum - ENUM
2025-07-17 12:34:04.135 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=chargetype, 规则ID=common.chargetype.enum, 规则名称=枚举值校验
2025-07-17 12:34:04.135 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 chargetype 添加了规则: ENUM
2025-07-17 12:34:04.135 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.phonenumber.format - FORMAT
2025-07-17 12:34:04.135 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-17 12:34:04.135 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 phonenumber 添加了规则: FORMAT
2025-07-17 12:34:04.135 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.phonenumber.length - LENGTH
2025-07-17 12:34:04.135 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-17 12:34:04.136 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 phonenumber 添加了规则: LENGTH
2025-07-17 12:34:04.136 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.mcnnumber.format - FORMAT
2025-07-17 12:34:04.136 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-17 12:34:04.136 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 mcnnumber 添加了规则: FORMAT
2025-07-17 12:34:04.136 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.sendorrcenum.format - FORMAT
2025-07-17 12:34:04.136 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=sendorreceNum, 规则ID=common.sendorreceNum.format, 规则名称=格式校验
2025-07-17 12:34:04.136 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 sendorreceNum 添加了规则: FORMAT
2025-07-17 12:34:04.136 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.sendorrcenum.length - LENGTH
2025-07-17 12:34:04.136 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=sendorreceNum, 规则ID=common.sendorreceNum.length, 规则名称=长度校验
2025-07-17 12:34:04.136 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 sendorreceNum 添加了规则: LENGTH
2025-07-17 12:34:04.136 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.optime.format - FORMAT
2025-07-17 12:34:04.136 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=optime, 规则ID=common.optime.format, 规则名称=格式校验
2025-07-17 12:34:04.136 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 optime 添加了规则: FORMAT
2025-07-17 12:34:04.136 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24206, 字段数量=5
2025-07-17 12:34:04.136 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 注册了 5 个字段的元数据
2025-07-17 12:34:04.136 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [optime, phonenumber, chargetype, mcnnumber, sendorreceNum] -> VGOP1-R2-10-24206
2025-07-17 12:34:04.136 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 注册了字段到接口的映射关系
2025-07-17 12:34:04.136 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24206 加载了 7 个字段校验规则
2025-07-17 12:34:04.136 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24207 的校验规则...
2025-07-17 12:34:04.136 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.phonenumber.format - FORMAT
2025-07-17 12:34:04.137 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-17 12:34:04.137 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 phonenumber 添加了规则: FORMAT
2025-07-17 12:34:04.137 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.phonenumber.length - LENGTH
2025-07-17 12:34:04.137 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-17 12:34:04.137 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 phonenumber 添加了规则: LENGTH
2025-07-17 12:34:04.137 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnnumber.format - FORMAT
2025-07-17 12:34:04.137 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-17 12:34:04.137 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnnumber 添加了规则: FORMAT
2025-07-17 12:34:04.137 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnnumber.length - LENGTH
2025-07-17 12:34:04.137 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnnumber, 规则ID=common.mcnnumber.length, 规则名称=长度校验
2025-07-17 12:34:04.137 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnnumber 添加了规则: LENGTH
2025-07-17 12:34:04.137 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnimsi.format - FORMAT
2025-07-17 12:34:04.137 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnImsi, 规则ID=common.mcnImsi.format, 规则名称=格式校验
2025-07-17 12:34:04.137 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnImsi 添加了规则: FORMAT
2025-07-17 12:34:04.137 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnimsi.length - LENGTH
2025-07-17 12:34:04.137 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnImsi, 规则ID=common.mcnImsi.length, 规则名称=长度校验
2025-07-17 12:34:04.137 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnImsi 添加了规则: LENGTH
2025-07-17 12:34:04.137 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.businessstate.enum - ENUM
2025-07-17 12:34:04.137 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=businessState, 规则ID=common.businessState.enum, 规则名称=枚举值校验
2025-07-17 12:34:04.137 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 businessState 添加了规则: ENUM
2025-07-17 12:34:04.138 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.numstate.format - FORMAT
2025-07-17 12:34:04.138 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=Numstate, 规则ID=common.Numstate.format, 规则名称=格式校验
2025-07-17 12:34:04.138 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 Numstate 添加了规则: FORMAT
2025-07-17 12:34:04.138 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.locationid.length - LENGTH
2025-07-17 12:34:04.138 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=Locationid, 规则ID=common.Locationid.length, 规则名称=长度校验
2025-07-17 12:34:04.138 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 Locationid 添加了规则: LENGTH
2025-07-17 12:34:04.138 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.bossprovinceid.format - FORMAT
2025-07-17 12:34:04.138 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=BossProvinceid, 规则ID=common.BossProvinceid.format, 规则名称=格式校验
2025-07-17 12:34:04.138 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 BossProvinceid 添加了规则: FORMAT
2025-07-17 12:34:04.138 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.bossprovinceid.range - RANGE
2025-07-17 12:34:04.138 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=BossProvinceid, 规则ID=common.BossProvinceid.range, 规则名称=范围校验
2025-07-17 12:34:04.138 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 BossProvinceid 添加了规则: RANGE
2025-07-17 12:34:04.138 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.openingtime.format - FORMAT
2025-07-17 12:34:04.138 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=openingtime, 规则ID=common.openingtime.format, 规则名称=格式校验
2025-07-17 12:34:04.138 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 openingtime 添加了规则: FORMAT
2025-07-17 12:34:04.138 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24207, 字段数量=8
2025-07-17 12:34:04.138 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 注册了 8 个字段的元数据
2025-07-17 12:34:04.138 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [Numstate, openingtime, mcnImsi, businessState, BossProvinceid, phonenumber, Locationid, mcnnumber] -> VGOP1-R2-10-24207
2025-07-17 12:34:04.138 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 注册了字段到接口的映射关系
2025-07-17 12:34:04.138 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24207 加载了 12 个字段校验规则
2025-07-17 12:34:04.138 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-11-24101 的校验规则...
2025-07-17 12:34:04.138 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.phonenum.format - FORMAT
2025-07-17 12:34:04.138 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=phonenum, 规则ID=common.phonenum.format, 规则名称=格式校验
2025-07-17 12:34:04.138 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 phonenum 添加了规则: FORMAT
2025-07-17 12:34:04.139 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.phonenum.range - RANGE
2025-07-17 12:34:04.139 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=phonenum, 规则ID=common.phonenum.range, 规则名称=范围校验
2025-07-17 12:34:04.139 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 phonenum 添加了规则: RANGE
2025-07-17 12:34:04.139 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnnum.format - FORMAT
2025-07-17 12:34:04.139 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnnum, 规则ID=common.mcnnum.format, 规则名称=格式校验
2025-07-17 12:34:04.139 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnnum 添加了规则: FORMAT
2025-07-17 12:34:04.139 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnnum.range - RANGE
2025-07-17 12:34:04.139 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnnum, 规则ID=common.mcnnum.range, 规则名称=范围校验
2025-07-17 12:34:04.139 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnnum 添加了规则: RANGE
2025-07-17 12:34:04.139 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.appactivenum.format - FORMAT
2025-07-17 12:34:04.139 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=appactivenum, 规则ID=common.appactivenum.format, 规则名称=格式校验
2025-07-17 12:34:04.139 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 appactivenum 添加了规则: FORMAT
2025-07-17 12:34:04.139 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.appactivenum.range - RANGE
2025-07-17 12:34:04.139 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=appactivenum, 规则ID=common.appactivenum.range, 规则名称=范围校验
2025-07-17 12:34:04.139 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 appactivenum 添加了规则: RANGE
2025-07-17 12:34:04.139 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnactivenum.format - FORMAT
2025-07-17 12:34:04.139 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnactivenum, 规则ID=common.mcnactivenum.format, 规则名称=格式校验
2025-07-17 12:34:04.139 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnactivenum 添加了规则: FORMAT
2025-07-17 12:34:04.139 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnactivenum.range - RANGE
2025-07-17 12:34:04.139 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnactivenum, 规则ID=common.mcnactivenum.range, 规则名称=范围校验
2025-07-17 12:34:04.139 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnactivenum 添加了规则: RANGE
2025-07-17 12:34:04.139 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.paynum.format - FORMAT
2025-07-17 12:34:04.139 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=paynum, 规则ID=common.paynum.format, 规则名称=格式校验
2025-07-17 12:34:04.139 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 paynum 添加了规则: FORMAT
2025-07-17 12:34:04.139 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.paynum.range - RANGE
2025-07-17 12:34:04.139 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=paynum, 规则ID=common.paynum.range, 规则名称=范围校验
2025-07-17 12:34:04.140 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 paynum 添加了规则: RANGE
2025-07-17 12:34:04.140 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.feenum.format - FORMAT
2025-07-17 12:34:04.140 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=feenum, 规则ID=common.feenum.format, 规则名称=格式校验
2025-07-17 12:34:04.140 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 feenum 添加了规则: FORMAT
2025-07-17 12:34:04.140 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.feenum.range - RANGE
2025-07-17 12:34:04.140 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=feenum, 规则ID=common.feenum.range, 规则名称=范围校验
2025-07-17 12:34:04.140 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 feenum 添加了规则: RANGE
2025-07-17 12:34:04.140 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secphonenum.format - FORMAT
2025-07-17 12:34:04.140 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secphonenum, 规则ID=common.secphonenum.format, 规则名称=格式校验
2025-07-17 12:34:04.140 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secphonenum 添加了规则: FORMAT
2025-07-17 12:34:04.140 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secphonenum.range - RANGE
2025-07-17 12:34:04.140 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secphonenum, 规则ID=common.secphonenum.range, 规则名称=范围校验
2025-07-17 12:34:04.140 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secphonenum 添加了规则: RANGE
2025-07-17 12:34:04.140 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secmcnnum.format - FORMAT
2025-07-17 12:34:04.140 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secmcnnum, 规则ID=common.secmcnnum.format, 规则名称=格式校验
2025-07-17 12:34:04.140 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secmcnnum 添加了规则: FORMAT
2025-07-17 12:34:04.140 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secmcnnum.range - RANGE
2025-07-17 12:34:04.140 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secmcnnum, 规则ID=common.secmcnnum.range, 规则名称=范围校验
2025-07-17 12:34:04.140 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secmcnnum 添加了规则: RANGE
2025-07-17 12:34:04.140 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.amcnnum.format - FORMAT
2025-07-17 12:34:04.140 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=amcnnum, 规则ID=common.amcnnum.format, 规则名称=格式校验
2025-07-17 12:34:04.140 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 amcnnum 添加了规则: FORMAT
2025-07-17 12:34:04.140 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.amcnnum.range - RANGE
2025-07-17 12:34:04.140 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=amcnnum, 规则ID=common.amcnnum.range, 规则名称=范围校验
2025-07-17 12:34:04.140 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 amcnnum 添加了规则: RANGE
2025-07-17 12:34:04.140 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-11-24101, 字段数量=9
2025-07-17 12:34:04.140 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 注册了 9 个字段的元数据
2025-07-17 12:34:04.140 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [appactivenum, paynum, mcnnum, secphonenum, phonenum, mcnactivenum, secmcnnum, amcnnum, feenum] -> VGOP1-R2-11-24101
2025-07-17 12:34:04.141 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 注册了字段到接口的映射关系
2025-07-17 12:34:04.141 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-11-24101 加载了 18 个字段校验规则
2025-07-17 12:34:04.141 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-13-24301 的校验规则...
2025-07-17 12:34:04.141 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servid.format - FORMAT
2025-07-17 12:34:04.141 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServID, 规则ID=common.ServID.format, 规则名称=格式校验
2025-07-17 12:34:04.141 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServID 添加了规则: FORMAT
2025-07-17 12:34:04.141 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servid.length - LENGTH
2025-07-17 12:34:04.141 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServID, 规则ID=common.ServID.length, 规则名称=长度校验
2025-07-17 12:34:04.141 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServID 添加了规则: LENGTH
2025-07-17 12:34:04.141 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servid.standard_format - LENGTH
2025-07-17 12:34:04.141 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServID, 规则ID=common.ServID.length, 规则名称=长度校验
2025-07-17 12:34:04.141 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServID 添加了规则: LENGTH
2025-07-17 12:34:04.141 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servname.length - LENGTH
2025-07-17 12:34:04.141 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServName, 规则ID=common.ServName.length, 规则名称=长度校验
2025-07-17 12:34:04.141 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServName 添加了规则: LENGTH
2025-07-17 12:34:04.141 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servname.content - FORMAT
2025-07-17 12:34:04.141 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServName, 规则ID=common.ServName.format, 规则名称=格式校验
2025-07-17 12:34:04.141 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServName 添加了规则: FORMAT
2025-07-17 12:34:04.141 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-13-24301, 字段数量=2
2025-07-17 12:34:04.141 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 注册了 2 个字段的元数据
2025-07-17 12:34:04.141 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [ServName, ServID] -> VGOP1-R2-13-24301
2025-07-17 12:34:04.141 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 注册了字段到接口的映射关系
2025-07-17 12:34:04.141 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-13-24301 加载了 5 个字段校验规则
2025-07-17 12:34:04.141 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-13-24302 的校验规则...
2025-07-17 12:34:04.141 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelcode.format - FORMAT
2025-07-17 12:34:04.141 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelcode, 规则ID=common.channelcode.format, 规则名称=格式校验
2025-07-17 12:34:04.141 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelcode 添加了规则: FORMAT
2025-07-17 12:34:04.141 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelcode.length - LENGTH
2025-07-17 12:34:04.141 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelcode, 规则ID=common.channelcode.length, 规则名称=长度校验
2025-07-17 12:34:04.141 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelcode 添加了规则: LENGTH
2025-07-17 12:34:04.142 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelcode.range - RANGE
2025-07-17 12:34:04.142 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelcode, 规则ID=common.channelcode.range, 规则名称=范围校验
2025-07-17 12:34:04.142 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelcode 添加了规则: RANGE
2025-07-17 12:34:04.142 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelname.length - LENGTH
2025-07-17 12:34:04.142 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelname, 规则ID=common.channelname.length, 规则名称=长度校验
2025-07-17 12:34:04.142 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelname 添加了规则: LENGTH
2025-07-17 12:34:04.142 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelname.content - FORMAT
2025-07-17 12:34:04.142 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelname, 规则ID=common.channelname.format, 规则名称=格式校验
2025-07-17 12:34:04.142 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelname 添加了规则: FORMAT
2025-07-17 12:34:04.142 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-13-24302, 字段数量=2
2025-07-17 12:34:04.142 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 注册了 2 个字段的元数据
2025-07-17 12:34:04.142 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [channelname, channelcode] -> VGOP1-R2-13-24302
2025-07-17 12:34:04.142 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 注册了字段到接口的映射关系
2025-07-17 12:34:04.142 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-13-24302 加载了 5 个字段校验规则
2025-07-17 12:34:04.142 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-13-24303 的校验规则...
2025-07-17 12:34:04.142 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdown.format - FORMAT
2025-07-17 12:34:04.142 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdown, 规则ID=common.shutdown.format, 规则名称=格式校验
2025-07-17 12:34:04.142 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdown 添加了规则: FORMAT
2025-07-17 12:34:04.142 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdown.length - LENGTH
2025-07-17 12:34:04.142 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdown, 规则ID=common.shutdown.length, 规则名称=长度校验
2025-07-17 12:34:04.142 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdown 添加了规则: LENGTH
2025-07-17 12:34:04.142 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdown.range - RANGE
2025-07-17 12:34:04.142 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdown, 规则ID=common.shutdown.range, 规则名称=范围校验
2025-07-17 12:34:04.142 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdown 添加了规则: RANGE
2025-07-17 12:34:04.142 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdownname.length - LENGTH
2025-07-17 12:34:04.142 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdownname, 规则ID=common.shutdownname.length, 规则名称=长度校验
2025-07-17 12:34:04.142 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdownname 添加了规则: LENGTH
2025-07-17 12:34:04.142 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdownname.content - FORMAT
2025-07-17 12:34:04.142 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdownname, 规则ID=common.shutdownname.format, 规则名称=格式校验
2025-07-17 12:34:04.143 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdownname 添加了规则: FORMAT
2025-07-17 12:34:04.143 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-13-24303, 字段数量=2
2025-07-17 12:34:04.143 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 注册了 2 个字段的元数据
2025-07-17 12:34:04.143 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [shutdownname, shutdown] -> VGOP1-R2-13-24303
2025-07-17 12:34:04.143 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 注册了字段到接口的映射关系
2025-07-17 12:34:04.143 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-13-24303 加载了 5 个字段校验规则
2025-07-17 12:34:04.143 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 校验规则加载完成，共加载 100 个规则
2025-07-17 12:34:25.366 [Thread-553] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-17 12:34:25.882 [Thread-553] INFO  o.s.s.quartz.SchedulerFactoryBean [||||] [] - Shutting down Quartz Scheduler
2025-07-17 12:34:25.882 [Thread-553] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-17 12:34:25.883 [Thread-553] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-17 12:34:25.883 [Thread-553] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-17 12:34:25.883 [Thread-553] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-6} closing ...
2025-07-17 12:34:26.944 [Thread-553] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-6} closed
2025-07-17 12:34:26.945 [Thread-553] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-5} closing ...
2025-07-17 12:34:27.002 [Thread-553] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-5} closed
2025-07-17 12:34:27.402 [restartedMain] INFO  c.v.service.VgopServiceApplication [||||] [] - Starting VgopServiceApplication using Java 1.8.0_452 on ziber with PID 25692 (C:\workspaces\hdh\vgop-vli\vgop-service\target\classes started by galil in C:\workspaces\hdh\vgop-vli\vgop-service)
2025-07-17 12:34:27.403 [restartedMain] DEBUG c.v.service.VgopServiceApplication [||||] [] - Running with Spring Boot v2.5.5, Spring v5.3.19
2025-07-17 12:34:27.403 [restartedMain] INFO  c.v.service.VgopServiceApplication [||||] [] - The following profiles are active: dev
2025-07-17 12:34:28.151 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\DataExportMapper.class]
2025-07-17 12:34:28.152 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\RevisionMapper.class]
2025-07-17 12:34:28.152 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\ValidationAlertsMapper.class]
2025-07-17 12:34:28.152 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\primary\RevisionTimesMapper.class]
2025-07-17 12:34:28.152 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\secondary\TaskExecutionMapper.class]
2025-07-17 12:34:28.152 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'dataExportMapper' and 'com.vgop.service.dao.DataExportMapper' mapperInterface
2025-07-17 12:34:28.152 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'revisionMapper' and 'com.vgop.service.dao.RevisionMapper' mapperInterface
2025-07-17 12:34:28.152 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'validationAlertsMapper' and 'com.vgop.service.dao.ValidationAlertsMapper' mapperInterface
2025-07-17 12:34:28.152 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'revisionTimesMapper' and 'com.vgop.service.dao.primary.RevisionTimesMapper' mapperInterface
2025-07-17 12:34:28.152 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'taskExecutionMapper' and 'com.vgop.service.dao.secondary.TaskExecutionMapper' mapperInterface
2025-07-17 12:34:28.186 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [||||] [] - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-17 12:34:28.448 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer [||||] [] - Tomcat initialized with port(s): 8080 (http)
2025-07-17 12:34:28.449 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol [||||] [] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-17 12:34:28.449 [restartedMain] INFO  o.a.catalina.core.StandardService [||||] [] - Starting service [Tomcat]
2025-07-17 12:34:28.449 [restartedMain] INFO  o.a.catalina.core.StandardEngine [||||] [] - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-17 12:34:28.504 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/vgop] [||||] [] - Initializing Spring embedded WebApplicationContext
2025-07-17 12:34:28.504 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext [||||] [] - Root WebApplicationContext: initialization completed in 1096 ms
2025-07-17 12:34:28.566 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - ValidationEngine已初始化，等待规则配置...
2025-07-17 12:34:29.566 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\mapper\DataExportMapper.xml]'
2025-07-17 12:34:29.573 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\mapper\RevisionMapper.xml]'
2025-07-17 12:34:29.582 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\mapper\TaskExecutionMapper.xml]'
2025-07-17 12:34:29.592 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\mapper\ValidationAlertsMapper.xml]'
2025-07-17 12:34:29.732 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - === 开始测试数据库连接 ===
2025-07-17 12:34:29.732 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 测试主数据源连接...
2025-07-17 12:34:29.733 [restartedMain] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - testWhileIdle is true, validationQuery not set
2025-07-17 12:34:29.734 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-7} inited
2025-07-17 12:34:30.314 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 主数据源连接成功: {databaseProductVersion=12.10.FC4G1AEE, databaseProductName=GBase Server, driverVersion=4.10.JC4G1N999, name=主数据源, driverName=GBase JDBC Driver for GBase Server, userName=ismp, url=jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;}
2025-07-17 12:34:30.314 [restartedMain] INFO  com.vgop.service.util.DatabaseUtil [||||] [] - 检测到数据库产品: GBase Server, URL: jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;
2025-07-17 12:34:30.315 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 检测到主数据库类型: gbase
2025-07-17 12:34:30.315 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 是否支持UNLOAD命令: true
2025-07-17 12:34:30.315 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 测试次数据源连接...
2025-07-17 12:34:30.315 [restartedMain] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - testWhileIdle is true, validationQuery not set
2025-07-17 12:34:30.315 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-8} inited
2025-07-17 12:34:30.916 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 次数据源连接成功: {databaseProductVersion=12.10.FC4G1AEE, databaseProductName=GBase Server, driverVersion=4.10.JC4G1N999, name=次数据源, driverName=GBase JDBC Driver for GBase Server, userName=ismp, url=jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;}
2025-07-17 12:34:30.916 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - === 数据库连接测试完成 ===
2025-07-17 12:34:31.077 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory [||||] [] - Using default implementation for ThreadExecutor
2025-07-17 12:34:31.078 [restartedMain] INFO  o.quartz.core.SchedulerSignalerImpl [||||] [] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-17 12:34:31.078 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - Quartz Scheduler v.2.3.2 created.
2025-07-17 12:34:31.078 [restartedMain] INFO  org.quartz.simpl.RAMJobStore [||||] [] - RAMJobStore initialized.
2025-07-17 12:34:31.078 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-17 12:34:31.078 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory [||||] [] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-17 12:34:31.078 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory [||||] [] - Quartz scheduler version: 2.3.2
2025-07-17 12:34:31.078 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@63f2284d
2025-07-17 12:34:31.111 [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver [||||] [] - Exposing 15 endpoint(s) beneath base path '/actuator'
2025-07-17 12:34:31.147 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:34:31.148 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库产品名称: GBase Server, 版本: 12.10.FC4G1AEE
2025-07-17 12:34:31.148 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 检测到数据库类型: GBase Server
2025-07-17 12:34:31.148 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 配置GBase数据库字符编码设置
2025-07-17 12:34:31.149 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]
2025-07-17 12:34:31.149 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:34:31.207 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Looking up default SQLErrorCodes for DataSource [com.alibaba.druid.pool.DruidDataSource@19e536a2]
2025-07-17 12:34:31.207 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:34:31.207 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - SQL error codes for 'GBase Server' not found
2025-07-17 12:34:31.207 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Caching SQL error codes for DataSource [com.alibaba.druid.pool.DruidDataSource@19e536a2]: database product name is 'GBase Server'
2025-07-17 12:34:31.207 [restartedMain] DEBUG o.s.j.s.SQLErrorCodeSQLExceptionTranslator [||||] [] - Unable to translate SQLException with Error code '-728', will now try the fallback translator
2025-07-17 12:34:31.207 [restartedMain] DEBUG o.s.j.s.SQLStateSQLExceptionTranslator [||||] [] - Extracted SQL state class 'IX' from value 'IX000'
2025-07-17 12:34:31.207 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 查询GBase数据库字符编码信息失败: StatementCallback; uncategorized SQLException for SQL [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]; SQL state [IX000]; error code [-728]; Unknown first argument of dbinfo(dblocale).; nested exception is java.sql.SQLException: Unknown first argument of dbinfo(dblocale).
2025-07-17 12:34:31.207 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - GBase数据库字符编码配置完成，依赖JDBC URL参数: DB_LOCALE, NEWCODESET
2025-07-17 12:34:31.207 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dbhostname') FROM systables WHERE tabid = 1]
2025-07-17 12:34:31.207 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:34:31.514 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库主机名: eb33207
2025-07-17 12:34:31.514 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT LENGTH('测试中文字符') FROM systables WHERE tabid = 1]
2025-07-17 12:34:31.514 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:34:31.796 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 字符编码兼容性测试通过，中文字符长度: 12
2025-07-17 12:34:31.796 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 已配置主数据源字符编码安全的JdbcTemplate
2025-07-17 12:34:31.798 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:34:31.798 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库产品名称: GBase Server, 版本: 12.10.FC4G1AEE
2025-07-17 12:34:31.799 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 检测到数据库类型: GBase Server
2025-07-17 12:34:31.799 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 配置GBase数据库字符编码设置
2025-07-17 12:34:31.799 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]
2025-07-17 12:34:31.799 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:34:31.857 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Looking up default SQLErrorCodes for DataSource [com.alibaba.druid.pool.DruidDataSource@67d5b334]
2025-07-17 12:34:31.857 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:34:31.858 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - SQL error codes for 'GBase Server' not found
2025-07-17 12:34:31.858 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Caching SQL error codes for DataSource [com.alibaba.druid.pool.DruidDataSource@67d5b334]: database product name is 'GBase Server'
2025-07-17 12:34:31.858 [restartedMain] DEBUG o.s.j.s.SQLErrorCodeSQLExceptionTranslator [||||] [] - Unable to translate SQLException with Error code '-728', will now try the fallback translator
2025-07-17 12:34:31.858 [restartedMain] DEBUG o.s.j.s.SQLStateSQLExceptionTranslator [||||] [] - Extracted SQL state class 'IX' from value 'IX000'
2025-07-17 12:34:31.858 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 查询GBase数据库字符编码信息失败: StatementCallback; uncategorized SQLException for SQL [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]; SQL state [IX000]; error code [-728]; Unknown first argument of dbinfo(dblocale).; nested exception is java.sql.SQLException: Unknown first argument of dbinfo(dblocale).
2025-07-17 12:34:31.858 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - GBase数据库字符编码配置完成，依赖JDBC URL参数: DB_LOCALE, NEWCODESET
2025-07-17 12:34:31.858 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dbhostname') FROM systables WHERE tabid = 1]
2025-07-17 12:34:31.858 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:34:32.149 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库主机名: eb33207
2025-07-17 12:34:32.149 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT LENGTH('测试中文字符') FROM systables WHERE tabid = 1]
2025-07-17 12:34:32.149 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:34:32.437 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 字符编码兼容性测试通过，中文字符长度: 12
2025-07-17 12:34:32.437 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 已配置次数据源字符编码安全的JdbcTemplate
2025-07-17 12:34:32.480 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer [||||] [] - LiveReload server is running on port 35729
2025-07-17 12:34:32.633 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol [||||] [] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-17 12:34:32.635 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer [||||] [] - Tomcat started on port(s): 8080 (http) with context path '/vgop'
2025-07-17 12:34:32.829 [restartedMain] INFO  o.s.s.quartz.SchedulerFactoryBean [||||] [] - Starting Quartz Scheduler now
2025-07-17 12:34:32.829 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-17 12:34:32.839 [restartedMain] INFO  c.v.service.VgopServiceApplication [||||] [] - Started VgopServiceApplication in 5.594 seconds (JVM running for 1262.538)
2025-07-17 12:34:32.840 [restartedMain] INFO  c.v.s.s.ConfigValidationService [||||] [] - 开始验证应用配置...
2025-07-17 12:34:32.841 [restartedMain] INFO  c.v.s.s.ConfigValidationService [||||] [] - 配置验证通过
2025-07-17 12:34:32.841 [restartedMain] INFO  c.v.s.service.DirectoryInitService [||||] [] - 开始初始化目录结构...
2025-07-17 12:34:32.841 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 数据导出根目录 - ./VGOPdata/datafile/
2025-07-17 12:34:32.841 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日志根目录 - ./logs/
2025-07-17 12:34:32.842 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 备份根目录 - ./data/backup/
2025-07-17 12:34:32.842 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 告警文件目录 - ./data/alerts/
2025-07-17 12:34:32.842 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250714 - ./VGOPdata/datafile//20250714/day
2025-07-17 12:34:32.843 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250714 - ./VGOPdata/datafile//20250714/month
2025-07-17 12:34:32.843 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250715 - ./VGOPdata/datafile//20250715/day
2025-07-17 12:34:32.843 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250715 - ./VGOPdata/datafile//20250715/month
2025-07-17 12:34:32.843 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250716 - ./VGOPdata/datafile//20250716/day
2025-07-17 12:34:32.844 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250716 - ./VGOPdata/datafile//20250716/month
2025-07-17 12:34:32.844 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250717 - ./VGOPdata/datafile//20250717/day
2025-07-17 12:34:32.844 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250717 - ./VGOPdata/datafile//20250717/month
2025-07-17 12:34:32.845 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250718 - ./VGOPdata/datafile//20250718/day
2025-07-17 12:34:32.845 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250718 - ./VGOPdata/datafile//20250718/month
2025-07-17 12:34:32.845 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250719 - ./VGOPdata/datafile//20250719/day
2025-07-17 12:34:32.845 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250719 - ./VGOPdata/datafile//20250719/month
2025-07-17 12:34:32.846 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月度数据目录 - 202507 - ./VGOPdata/datafile//202507
2025-07-17 12:34:32.846 [restartedMain] INFO  c.v.s.service.DirectoryInitService [||||] [] - 目录结构初始化完成
2025-07-17 12:34:32.847 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener [||||] [] - Condition evaluation unchanged
2025-07-17 12:34:32.847 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 开始从配置文件加载校验规则...
2025-07-17 12:34:32.847 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24201 的校验规则...
2025-07-17 12:34:32.847 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phonenumber.format - FORMAT
2025-07-17 12:34:32.848 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-17 12:34:32.848 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phonenumber 添加了规则: FORMAT
2025-07-17 12:34:32.848 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phonenumber.length - LENGTH
2025-07-17 12:34:32.848 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-17 12:34:32.848 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phonenumber 添加了规则: LENGTH
2025-07-17 12:34:32.848 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phonestate.enum - ENUM
2025-07-17 12:34:32.848 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phonestate, 规则ID=common.phonestate.enum, 规则名称=枚举值校验
2025-07-17 12:34:32.848 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phonestate 添加了规则: ENUM
2025-07-17 12:34:32.848 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phoneimsi.length - LENGTH
2025-07-17 12:34:32.848 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phoneimsi, 规则ID=common.phoneimsi.length, 规则名称=长度校验
2025-07-17 12:34:32.848 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phoneimsi 添加了规则: LENGTH
2025-07-17 12:34:32.848 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phoneimei.length - LENGTH
2025-07-17 12:34:32.848 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phoneimei, 规则ID=common.phoneimei.length, 规则名称=长度校验
2025-07-17 12:34:32.848 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phoneimei 添加了规则: LENGTH
2025-07-17 12:34:32.848 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.locationid.length - LENGTH
2025-07-17 12:34:32.848 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=locationid, 规则ID=common.locationid.length, 规则名称=长度校验
2025-07-17 12:34:32.848 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 locationid 添加了规则: LENGTH
2025-07-17 12:34:32.849 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.provinceid.length - LENGTH
2025-07-17 12:34:32.849 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=provinceid, 规则ID=common.provinceid.length, 规则名称=长度校验
2025-07-17 12:34:32.849 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 provinceid 添加了规则: LENGTH
2025-07-17 12:34:32.849 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.openingtime.format - FORMAT
2025-07-17 12:34:32.849 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=openingtime, 规则ID=common.openingtime.format, 规则名称=格式校验
2025-07-17 12:34:32.849 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 openingtime 添加了规则: FORMAT
2025-07-17 12:34:32.849 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.optime.format - FORMAT
2025-07-17 12:34:32.849 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=Optime, 规则ID=common.Optime.format, 规则名称=格式校验
2025-07-17 12:34:32.849 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 Optime 添加了规则: FORMAT
2025-07-17 12:34:32.849 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.sex.enum - ENUM
2025-07-17 12:34:32.849 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=sex, 规则ID=common.sex.enum, 规则名称=枚举值校验
2025-07-17 12:34:32.849 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 sex 添加了规则: ENUM
2025-07-17 12:34:32.849 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24201, 字段数量=9
2025-07-17 12:34:32.849 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 注册了 9 个字段的元数据
2025-07-17 12:34:32.849 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [phonestate, Optime, phoneimei, openingtime, locationid, sex, phonenumber, phoneimsi, provinceid] -> VGOP1-R2-10-24201
2025-07-17 12:34:32.849 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 注册了字段到接口的映射关系
2025-07-17 12:34:32.850 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24201 加载了 10 个字段校验规则
2025-07-17 12:34:32.850 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24202 的校验规则...
2025-07-17 12:34:32.850 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnumber.format - FORMAT
2025-07-17 12:34:32.850 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-17 12:34:32.850 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnumber 添加了规则: FORMAT
2025-07-17 12:34:32.850 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnumber.length - LENGTH
2025-07-17 12:34:32.850 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnumber, 规则ID=common.mcnnumber.length, 规则名称=长度校验
2025-07-17 12:34:32.850 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnumber 添加了规则: LENGTH
2025-07-17 12:34:32.850 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.phonenumber.format - FORMAT
2025-07-17 12:34:32.850 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-17 12:34:32.850 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 phonenumber 添加了规则: FORMAT
2025-07-17 12:34:32.850 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.phonenumber.length - LENGTH
2025-07-17 12:34:32.850 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-17 12:34:32.850 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 phonenumber 添加了规则: LENGTH
2025-07-17 12:34:32.850 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.business.enum - ENUM
2025-07-17 12:34:32.850 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=business, 规则ID=common.business.enum, 规则名称=枚举值校验
2025-07-17 12:34:32.850 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 business 添加了规则: ENUM
2025-07-17 12:34:32.850 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.shutdown.length - LENGTH
2025-07-17 12:34:32.850 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=shutdown, 规则ID=common.shutdown.length, 规则名称=长度校验
2025-07-17 12:34:32.850 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 shutdown 添加了规则: LENGTH
2025-07-17 12:34:32.850 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnimsi.length - LENGTH
2025-07-17 12:34:32.850 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnimsi, 规则ID=common.mcnimsi.length, 规则名称=长度校验
2025-07-17 12:34:32.850 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnimsi 添加了规则: LENGTH
2025-07-17 12:34:32.850 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnimsi.format - FORMAT
2025-07-17 12:34:32.850 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnimsi, 规则ID=common.mcnimsi.format, 规则名称=格式校验
2025-07-17 12:34:32.850 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnimsi 添加了规则: FORMAT
2025-07-17 12:34:32.850 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnlocationid.length - LENGTH
2025-07-17 12:34:32.851 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnlocationid, 规则ID=common.mcnlocationid.length, 规则名称=长度校验
2025-07-17 12:34:32.851 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnlocationid 添加了规则: LENGTH
2025-07-17 12:34:32.851 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.numstate.length - LENGTH
2025-07-17 12:34:32.851 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=numstate, 规则ID=common.numstate.length, 规则名称=长度校验
2025-07-17 12:34:32.851 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 numstate 添加了规则: LENGTH
2025-07-17 12:34:32.851 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnature.enum - ENUM
2025-07-17 12:34:32.851 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnature, 规则ID=common.mcnnature.enum, 规则名称=枚举值校验
2025-07-17 12:34:32.851 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnature 添加了规则: ENUM
2025-07-17 12:34:32.851 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnum.enum - ENUM
2025-07-17 12:34:32.851 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnum, 规则ID=common.mcnnum.enum, 规则名称=枚举值校验
2025-07-17 12:34:32.851 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnum 添加了规则: ENUM
2025-07-17 12:34:32.851 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnum.length - LENGTH
2025-07-17 12:34:32.851 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnum, 规则ID=common.mcnnum.length, 规则名称=长度校验
2025-07-17 12:34:32.851 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnum 添加了规则: LENGTH
2025-07-17 12:34:32.853 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.channel.length - LENGTH
2025-07-17 12:34:32.853 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=channel, 规则ID=common.channel.length, 规则名称=长度校验
2025-07-17 12:34:32.853 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 channel 添加了规则: LENGTH
2025-07-17 12:34:32.853 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mj.enum - ENUM
2025-07-17 12:34:32.853 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mj, 规则ID=common.mj.enum, 规则名称=枚举值校验
2025-07-17 12:34:32.853 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mj 添加了规则: ENUM
2025-07-17 12:34:32.853 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.openingtime.format - FORMAT
2025-07-17 12:34:32.853 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=openingtime, 规则ID=common.openingtime.format, 规则名称=格式校验
2025-07-17 12:34:32.853 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 openingtime 添加了规则: FORMAT
2025-07-17 12:34:32.853 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.optime.format - FORMAT
2025-07-17 12:34:32.853 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=Optime, 规则ID=common.Optime.format, 规则名称=格式校验
2025-07-17 12:34:32.853 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 Optime 添加了规则: FORMAT
2025-07-17 12:34:32.853 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcimsitime.format - FORMAT
2025-07-17 12:34:32.853 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcimsitime, 规则ID=common.mcimsitime.format, 规则名称=格式校验
2025-07-17 12:34:32.853 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcimsitime 添加了规则: FORMAT
2025-07-17 12:34:32.853 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.usertype.length - LENGTH
2025-07-17 12:34:32.853 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=usertype, 规则ID=common.usertype.length, 规则名称=长度校验
2025-07-17 12:34:32.853 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 usertype 添加了规则: LENGTH
2025-07-17 12:34:32.853 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.begintime.format - FORMAT
2025-07-17 12:34:32.853 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=Begintime, 规则ID=common.Begintime.format, 规则名称=格式校验
2025-07-17 12:34:32.853 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 Begintime 添加了规则: FORMAT
2025-07-17 12:34:32.853 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.endtime.format - FORMAT
2025-07-17 12:34:32.853 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=Endtime, 规则ID=common.Endtime.format, 规则名称=格式校验
2025-07-17 12:34:32.853 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 Endtime 添加了规则: FORMAT
2025-07-17 12:34:32.854 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.servid.length - LENGTH
2025-07-17 12:34:32.854 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=ServID, 规则ID=common.ServID.length, 规则名称=长度校验
2025-07-17 12:34:32.854 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 ServID 添加了规则: LENGTH
2025-07-17 12:34:32.854 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24202, 字段数量=18
2025-07-17 12:34:32.854 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 注册了 18 个字段的元数据
2025-07-17 12:34:32.854 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [mcnlocationid, Optime, business, mcnimsi, mcimsitime, phonenumber, mcnnum, channel, usertype, numstate, Endtime, openingtime, ServID, mcnnature, Begintime, mcnnumber, mj, shutdown] -> VGOP1-R2-10-24202
2025-07-17 12:34:32.854 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 注册了字段到接口的映射关系
2025-07-17 12:34:32.854 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24202 加载了 22 个字段校验规则
2025-07-17 12:34:32.854 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24203 的校验规则...
2025-07-17 12:34:32.854 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.account_id.length - LENGTH
2025-07-17 12:34:32.854 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=account_id, 规则ID=common.account_id.length, 规则名称=长度校验
2025-07-17 12:34:32.854 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 account_id 添加了规则: LENGTH
2025-07-17 12:34:32.854 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.phonenumber.format - FORMAT
2025-07-17 12:34:32.854 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-17 12:34:32.854 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 phonenumber 添加了规则: FORMAT
2025-07-17 12:34:32.854 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.phonenumber.length - LENGTH
2025-07-17 12:34:32.854 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-17 12:34:32.854 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 phonenumber 添加了规则: LENGTH
2025-07-17 12:34:32.854 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.type.enum - ENUM
2025-07-17 12:34:32.854 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=type, 规则ID=common.type.enum, 规则名称=枚举值校验
2025-07-17 12:34:32.854 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 type 添加了规则: ENUM
2025-07-17 12:34:32.854 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.optime.format - FORMAT
2025-07-17 12:34:32.854 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=optime, 规则ID=common.optime.format, 规则名称=格式校验
2025-07-17 12:34:32.854 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 optime 添加了规则: FORMAT
2025-07-17 12:34:32.854 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.version.length - LENGTH
2025-07-17 12:34:32.855 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=version, 规则ID=common.version.length, 规则名称=长度校验
2025-07-17 12:34:32.855 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 version 添加了规则: LENGTH
2025-07-17 12:34:32.855 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24203, 字段数量=5
2025-07-17 12:34:32.855 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 注册了 5 个字段的元数据
2025-07-17 12:34:32.855 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [account_id, optime, phonenumber, type, version] -> VGOP1-R2-10-24203
2025-07-17 12:34:32.855 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 注册了字段到接口的映射关系
2025-07-17 12:34:32.855 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24203 加载了 6 个字段校验规则
2025-07-17 12:34:32.855 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24205 的校验规则...
2025-07-17 12:34:32.855 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.calltype.enum - ENUM
2025-07-17 12:34:32.855 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=callType, 规则ID=common.callType.enum, 规则名称=枚举值校验
2025-07-17 12:34:32.855 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 callType 添加了规则: ENUM
2025-07-17 12:34:32.855 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callingpartynumber.format - FORMAT
2025-07-17 12:34:32.855 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=callingPartyNumber, 规则ID=common.callingPartyNumber.format, 规则名称=格式校验
2025-07-17 12:34:32.855 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 callingPartyNumber 添加了规则: FORMAT
2025-07-17 12:34:32.855 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callingpartynumber.length - LENGTH
2025-07-17 12:34:32.855 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=callingPartyNumber, 规则ID=common.callingPartyNumber.length, 规则名称=长度校验
2025-07-17 12:34:32.855 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 callingPartyNumber 添加了规则: LENGTH
2025-07-17 12:34:32.855 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.calledpartynumber.format - FORMAT
2025-07-17 12:34:32.855 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=calledPartyNumber, 规则ID=common.calledPartyNumber.format, 规则名称=格式校验
2025-07-17 12:34:32.855 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 calledPartyNumber 添加了规则: FORMAT
2025-07-17 12:34:32.855 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.calledpartynumber.length - LENGTH
2025-07-17 12:34:32.855 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=calledPartyNumber, 规则ID=common.calledPartyNumber.length, 规则名称=长度校验
2025-07-17 12:34:32.855 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 calledPartyNumber 添加了规则: LENGTH
2025-07-17 12:34:32.855 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.mcnnumber.format - FORMAT
2025-07-17 12:34:32.855 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-17 12:34:32.855 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 mcnnumber 添加了规则: FORMAT
2025-07-17 12:34:32.855 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callbegintime.format - FORMAT
2025-07-17 12:34:32.855 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallBeginTime, 规则ID=common.CallBeginTime.format, 规则名称=格式校验
2025-07-17 12:34:32.855 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallBeginTime 添加了规则: FORMAT
2025-07-17 12:34:32.855 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callendtime.format - FORMAT
2025-07-17 12:34:32.856 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallEndTime, 规则ID=common.CallEndTime.format, 规则名称=格式校验
2025-07-17 12:34:32.856 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallEndTime 添加了规则: FORMAT
2025-07-17 12:34:32.856 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callduration.format - FORMAT
2025-07-17 12:34:32.856 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallDuration, 规则ID=common.CallDuration.format, 规则名称=格式校验
2025-07-17 12:34:32.856 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallDuration 添加了规则: FORMAT
2025-07-17 12:34:32.856 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callduration.range - RANGE
2025-07-17 12:34:32.856 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallDuration, 规则ID=common.CallDuration.range, 规则名称=范围校验
2025-07-17 12:34:32.856 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallDuration 添加了规则: RANGE
2025-07-17 12:34:32.856 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24205, 字段数量=7
2025-07-17 12:34:32.856 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 注册了 7 个字段的元数据
2025-07-17 12:34:32.856 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [CallEndTime, callingPartyNumber, calledPartyNumber, CallBeginTime, mcnnumber, CallDuration, callType] -> VGOP1-R2-10-24205
2025-07-17 12:34:32.856 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 注册了字段到接口的映射关系
2025-07-17 12:34:32.856 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24205 加载了 10 个字段校验规则
2025-07-17 12:34:32.856 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24206 的校验规则...
2025-07-17 12:34:32.856 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.chargetype.enum - ENUM
2025-07-17 12:34:32.856 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=chargetype, 规则ID=common.chargetype.enum, 规则名称=枚举值校验
2025-07-17 12:34:32.856 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 chargetype 添加了规则: ENUM
2025-07-17 12:34:32.856 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.phonenumber.format - FORMAT
2025-07-17 12:34:32.856 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-17 12:34:32.856 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 phonenumber 添加了规则: FORMAT
2025-07-17 12:34:32.856 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.phonenumber.length - LENGTH
2025-07-17 12:34:32.856 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-17 12:34:32.856 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 phonenumber 添加了规则: LENGTH
2025-07-17 12:34:32.856 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.mcnnumber.format - FORMAT
2025-07-17 12:34:32.856 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-17 12:34:32.856 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 mcnnumber 添加了规则: FORMAT
2025-07-17 12:34:32.856 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.sendorrcenum.format - FORMAT
2025-07-17 12:34:32.856 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=sendorreceNum, 规则ID=common.sendorreceNum.format, 规则名称=格式校验
2025-07-17 12:34:32.856 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 sendorreceNum 添加了规则: FORMAT
2025-07-17 12:34:32.856 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.sendorrcenum.length - LENGTH
2025-07-17 12:34:32.857 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=sendorreceNum, 规则ID=common.sendorreceNum.length, 规则名称=长度校验
2025-07-17 12:34:32.857 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 sendorreceNum 添加了规则: LENGTH
2025-07-17 12:34:32.857 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.optime.format - FORMAT
2025-07-17 12:34:32.857 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=optime, 规则ID=common.optime.format, 规则名称=格式校验
2025-07-17 12:34:32.857 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 optime 添加了规则: FORMAT
2025-07-17 12:34:32.857 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24206, 字段数量=5
2025-07-17 12:34:32.857 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 注册了 5 个字段的元数据
2025-07-17 12:34:32.857 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [optime, phonenumber, chargetype, mcnnumber, sendorreceNum] -> VGOP1-R2-10-24206
2025-07-17 12:34:32.857 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 注册了字段到接口的映射关系
2025-07-17 12:34:32.857 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24206 加载了 7 个字段校验规则
2025-07-17 12:34:32.857 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24207 的校验规则...
2025-07-17 12:34:32.857 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.phonenumber.format - FORMAT
2025-07-17 12:34:32.857 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-17 12:34:32.857 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 phonenumber 添加了规则: FORMAT
2025-07-17 12:34:32.857 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.phonenumber.length - LENGTH
2025-07-17 12:34:32.857 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-17 12:34:32.857 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 phonenumber 添加了规则: LENGTH
2025-07-17 12:34:32.857 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnnumber.format - FORMAT
2025-07-17 12:34:32.857 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-17 12:34:32.857 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnnumber 添加了规则: FORMAT
2025-07-17 12:34:32.857 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnnumber.length - LENGTH
2025-07-17 12:34:32.857 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnnumber, 规则ID=common.mcnnumber.length, 规则名称=长度校验
2025-07-17 12:34:32.857 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnnumber 添加了规则: LENGTH
2025-07-17 12:34:32.857 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnimsi.format - FORMAT
2025-07-17 12:34:32.857 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnImsi, 规则ID=common.mcnImsi.format, 规则名称=格式校验
2025-07-17 12:34:32.857 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnImsi 添加了规则: FORMAT
2025-07-17 12:34:32.857 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnimsi.length - LENGTH
2025-07-17 12:34:32.857 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnImsi, 规则ID=common.mcnImsi.length, 规则名称=长度校验
2025-07-17 12:34:32.858 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnImsi 添加了规则: LENGTH
2025-07-17 12:34:32.858 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.businessstate.enum - ENUM
2025-07-17 12:34:32.858 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=businessState, 规则ID=common.businessState.enum, 规则名称=枚举值校验
2025-07-17 12:34:32.858 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 businessState 添加了规则: ENUM
2025-07-17 12:34:32.858 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.numstate.format - FORMAT
2025-07-17 12:34:32.858 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=Numstate, 规则ID=common.Numstate.format, 规则名称=格式校验
2025-07-17 12:34:32.858 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 Numstate 添加了规则: FORMAT
2025-07-17 12:34:32.858 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.locationid.length - LENGTH
2025-07-17 12:34:32.858 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=Locationid, 规则ID=common.Locationid.length, 规则名称=长度校验
2025-07-17 12:34:32.858 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 Locationid 添加了规则: LENGTH
2025-07-17 12:34:32.858 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.bossprovinceid.format - FORMAT
2025-07-17 12:34:32.858 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=BossProvinceid, 规则ID=common.BossProvinceid.format, 规则名称=格式校验
2025-07-17 12:34:32.858 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 BossProvinceid 添加了规则: FORMAT
2025-07-17 12:34:32.858 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.bossprovinceid.range - RANGE
2025-07-17 12:34:32.858 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=BossProvinceid, 规则ID=common.BossProvinceid.range, 规则名称=范围校验
2025-07-17 12:34:32.858 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 BossProvinceid 添加了规则: RANGE
2025-07-17 12:34:32.858 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.openingtime.format - FORMAT
2025-07-17 12:34:32.858 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=openingtime, 规则ID=common.openingtime.format, 规则名称=格式校验
2025-07-17 12:34:32.858 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 openingtime 添加了规则: FORMAT
2025-07-17 12:34:32.858 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24207, 字段数量=8
2025-07-17 12:34:32.858 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 注册了 8 个字段的元数据
2025-07-17 12:34:32.858 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [Numstate, openingtime, mcnImsi, businessState, BossProvinceid, phonenumber, Locationid, mcnnumber] -> VGOP1-R2-10-24207
2025-07-17 12:34:32.858 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 注册了字段到接口的映射关系
2025-07-17 12:34:32.858 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24207 加载了 12 个字段校验规则
2025-07-17 12:34:32.858 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-11-24101 的校验规则...
2025-07-17 12:34:32.858 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.phonenum.format - FORMAT
2025-07-17 12:34:32.858 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=phonenum, 规则ID=common.phonenum.format, 规则名称=格式校验
2025-07-17 12:34:32.858 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 phonenum 添加了规则: FORMAT
2025-07-17 12:34:32.858 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.phonenum.range - RANGE
2025-07-17 12:34:32.858 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=phonenum, 规则ID=common.phonenum.range, 规则名称=范围校验
2025-07-17 12:34:32.858 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 phonenum 添加了规则: RANGE
2025-07-17 12:34:32.858 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnnum.format - FORMAT
2025-07-17 12:34:32.859 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnnum, 规则ID=common.mcnnum.format, 规则名称=格式校验
2025-07-17 12:34:32.859 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnnum 添加了规则: FORMAT
2025-07-17 12:34:32.859 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnnum.range - RANGE
2025-07-17 12:34:32.859 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnnum, 规则ID=common.mcnnum.range, 规则名称=范围校验
2025-07-17 12:34:32.859 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnnum 添加了规则: RANGE
2025-07-17 12:34:32.859 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.appactivenum.format - FORMAT
2025-07-17 12:34:32.859 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=appactivenum, 规则ID=common.appactivenum.format, 规则名称=格式校验
2025-07-17 12:34:32.859 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 appactivenum 添加了规则: FORMAT
2025-07-17 12:34:32.859 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.appactivenum.range - RANGE
2025-07-17 12:34:32.859 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=appactivenum, 规则ID=common.appactivenum.range, 规则名称=范围校验
2025-07-17 12:34:32.859 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 appactivenum 添加了规则: RANGE
2025-07-17 12:34:32.859 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnactivenum.format - FORMAT
2025-07-17 12:34:32.859 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnactivenum, 规则ID=common.mcnactivenum.format, 规则名称=格式校验
2025-07-17 12:34:32.859 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnactivenum 添加了规则: FORMAT
2025-07-17 12:34:32.859 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnactivenum.range - RANGE
2025-07-17 12:34:32.859 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnactivenum, 规则ID=common.mcnactivenum.range, 规则名称=范围校验
2025-07-17 12:34:32.859 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnactivenum 添加了规则: RANGE
2025-07-17 12:34:32.859 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.paynum.format - FORMAT
2025-07-17 12:34:32.859 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=paynum, 规则ID=common.paynum.format, 规则名称=格式校验
2025-07-17 12:34:32.859 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 paynum 添加了规则: FORMAT
2025-07-17 12:34:32.859 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.paynum.range - RANGE
2025-07-17 12:34:32.859 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=paynum, 规则ID=common.paynum.range, 规则名称=范围校验
2025-07-17 12:34:32.859 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 paynum 添加了规则: RANGE
2025-07-17 12:34:32.859 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.feenum.format - FORMAT
2025-07-17 12:34:32.859 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=feenum, 规则ID=common.feenum.format, 规则名称=格式校验
2025-07-17 12:34:32.859 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 feenum 添加了规则: FORMAT
2025-07-17 12:34:32.859 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.feenum.range - RANGE
2025-07-17 12:34:32.859 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=feenum, 规则ID=common.feenum.range, 规则名称=范围校验
2025-07-17 12:34:32.859 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 feenum 添加了规则: RANGE
2025-07-17 12:34:32.859 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secphonenum.format - FORMAT
2025-07-17 12:34:32.859 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secphonenum, 规则ID=common.secphonenum.format, 规则名称=格式校验
2025-07-17 12:34:32.859 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secphonenum 添加了规则: FORMAT
2025-07-17 12:34:32.859 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secphonenum.range - RANGE
2025-07-17 12:34:32.859 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secphonenum, 规则ID=common.secphonenum.range, 规则名称=范围校验
2025-07-17 12:34:32.859 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secphonenum 添加了规则: RANGE
2025-07-17 12:34:32.861 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secmcnnum.format - FORMAT
2025-07-17 12:34:32.861 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secmcnnum, 规则ID=common.secmcnnum.format, 规则名称=格式校验
2025-07-17 12:34:32.861 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secmcnnum 添加了规则: FORMAT
2025-07-17 12:34:32.861 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secmcnnum.range - RANGE
2025-07-17 12:34:32.861 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secmcnnum, 规则ID=common.secmcnnum.range, 规则名称=范围校验
2025-07-17 12:34:32.861 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secmcnnum 添加了规则: RANGE
2025-07-17 12:34:32.861 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.amcnnum.format - FORMAT
2025-07-17 12:34:32.861 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=amcnnum, 规则ID=common.amcnnum.format, 规则名称=格式校验
2025-07-17 12:34:32.861 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 amcnnum 添加了规则: FORMAT
2025-07-17 12:34:32.861 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.amcnnum.range - RANGE
2025-07-17 12:34:32.861 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=amcnnum, 规则ID=common.amcnnum.range, 规则名称=范围校验
2025-07-17 12:34:32.861 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 amcnnum 添加了规则: RANGE
2025-07-17 12:34:32.861 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-11-24101, 字段数量=9
2025-07-17 12:34:32.861 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 注册了 9 个字段的元数据
2025-07-17 12:34:32.861 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [appactivenum, paynum, mcnnum, secphonenum, phonenum, mcnactivenum, secmcnnum, amcnnum, feenum] -> VGOP1-R2-11-24101
2025-07-17 12:34:32.861 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 注册了字段到接口的映射关系
2025-07-17 12:34:32.861 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-11-24101 加载了 18 个字段校验规则
2025-07-17 12:34:32.861 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-13-24301 的校验规则...
2025-07-17 12:34:32.861 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servid.format - FORMAT
2025-07-17 12:34:32.861 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServID, 规则ID=common.ServID.format, 规则名称=格式校验
2025-07-17 12:34:32.861 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServID 添加了规则: FORMAT
2025-07-17 12:34:32.861 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servid.length - LENGTH
2025-07-17 12:34:32.861 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServID, 规则ID=common.ServID.length, 规则名称=长度校验
2025-07-17 12:34:32.861 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServID 添加了规则: LENGTH
2025-07-17 12:34:32.861 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servid.standard_format - LENGTH
2025-07-17 12:34:32.861 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServID, 规则ID=common.ServID.length, 规则名称=长度校验
2025-07-17 12:34:32.861 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServID 添加了规则: LENGTH
2025-07-17 12:34:32.861 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servname.length - LENGTH
2025-07-17 12:34:32.861 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServName, 规则ID=common.ServName.length, 规则名称=长度校验
2025-07-17 12:34:32.862 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServName 添加了规则: LENGTH
2025-07-17 12:34:32.862 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servname.content - FORMAT
2025-07-17 12:34:32.862 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServName, 规则ID=common.ServName.format, 规则名称=格式校验
2025-07-17 12:34:32.862 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServName 添加了规则: FORMAT
2025-07-17 12:34:32.862 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-13-24301, 字段数量=2
2025-07-17 12:34:32.862 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 注册了 2 个字段的元数据
2025-07-17 12:34:32.862 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [ServName, ServID] -> VGOP1-R2-13-24301
2025-07-17 12:34:32.862 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 注册了字段到接口的映射关系
2025-07-17 12:34:32.862 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-13-24301 加载了 5 个字段校验规则
2025-07-17 12:34:32.862 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-13-24302 的校验规则...
2025-07-17 12:34:32.862 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelcode.format - FORMAT
2025-07-17 12:34:32.862 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelcode, 规则ID=common.channelcode.format, 规则名称=格式校验
2025-07-17 12:34:32.862 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelcode 添加了规则: FORMAT
2025-07-17 12:34:32.862 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelcode.length - LENGTH
2025-07-17 12:34:32.862 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelcode, 规则ID=common.channelcode.length, 规则名称=长度校验
2025-07-17 12:34:32.862 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelcode 添加了规则: LENGTH
2025-07-17 12:34:32.862 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelcode.range - RANGE
2025-07-17 12:34:32.862 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelcode, 规则ID=common.channelcode.range, 规则名称=范围校验
2025-07-17 12:34:32.862 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelcode 添加了规则: RANGE
2025-07-17 12:34:32.862 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelname.length - LENGTH
2025-07-17 12:34:32.862 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelname, 规则ID=common.channelname.length, 规则名称=长度校验
2025-07-17 12:34:32.862 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelname 添加了规则: LENGTH
2025-07-17 12:34:32.862 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelname.content - FORMAT
2025-07-17 12:34:32.862 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelname, 规则ID=common.channelname.format, 规则名称=格式校验
2025-07-17 12:34:32.862 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelname 添加了规则: FORMAT
2025-07-17 12:34:32.862 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-13-24302, 字段数量=2
2025-07-17 12:34:32.862 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 注册了 2 个字段的元数据
2025-07-17 12:34:32.862 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [channelname, channelcode] -> VGOP1-R2-13-24302
2025-07-17 12:34:32.862 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 注册了字段到接口的映射关系
2025-07-17 12:34:32.862 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-13-24302 加载了 5 个字段校验规则
2025-07-17 12:34:32.863 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-13-24303 的校验规则...
2025-07-17 12:34:32.863 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdown.format - FORMAT
2025-07-17 12:34:32.863 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdown, 规则ID=common.shutdown.format, 规则名称=格式校验
2025-07-17 12:34:32.863 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdown 添加了规则: FORMAT
2025-07-17 12:34:32.863 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdown.length - LENGTH
2025-07-17 12:34:32.863 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdown, 规则ID=common.shutdown.length, 规则名称=长度校验
2025-07-17 12:34:32.863 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdown 添加了规则: LENGTH
2025-07-17 12:34:32.863 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdown.range - RANGE
2025-07-17 12:34:32.863 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdown, 规则ID=common.shutdown.range, 规则名称=范围校验
2025-07-17 12:34:32.863 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdown 添加了规则: RANGE
2025-07-17 12:34:32.863 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdownname.length - LENGTH
2025-07-17 12:34:32.863 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdownname, 规则ID=common.shutdownname.length, 规则名称=长度校验
2025-07-17 12:34:32.863 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdownname 添加了规则: LENGTH
2025-07-17 12:34:32.863 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdownname.content - FORMAT
2025-07-17 12:34:32.863 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdownname, 规则ID=common.shutdownname.format, 规则名称=格式校验
2025-07-17 12:34:32.863 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdownname 添加了规则: FORMAT
2025-07-17 12:34:32.863 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-13-24303, 字段数量=2
2025-07-17 12:34:32.863 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 注册了 2 个字段的元数据
2025-07-17 12:34:32.863 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [shutdownname, shutdown] -> VGOP1-R2-13-24303
2025-07-17 12:34:32.863 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 注册了字段到接口的映射关系
2025-07-17 12:34:32.863 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-13-24303 加载了 5 个字段校验规则
2025-07-17 12:34:32.863 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 校验规则加载完成，共加载 100 个规则
2025-07-17 12:37:47.872 [Thread-563] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-17 12:37:48.524 [Thread-563] INFO  o.s.s.quartz.SchedulerFactoryBean [||||] [] - Shutting down Quartz Scheduler
2025-07-17 12:37:48.524 [Thread-563] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-17 12:37:48.524 [Thread-563] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-17 12:37:48.524 [Thread-563] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-17 12:37:48.525 [Thread-563] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-8} closing ...
2025-07-17 12:37:48.597 [Thread-563] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-8} closed
2025-07-17 12:37:48.598 [Thread-563] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-7} closing ...
2025-07-17 12:37:48.688 [Thread-563] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-7} closed
2025-07-17 12:37:49.160 [restartedMain] INFO  c.v.service.VgopServiceApplication [||||] [] - Starting VgopServiceApplication using Java 1.8.0_452 on ziber with PID 25692 (C:\workspaces\hdh\vgop-vli\vgop-service\target\classes started by galil in C:\workspaces\hdh\vgop-vli\vgop-service)
2025-07-17 12:37:49.160 [restartedMain] DEBUG c.v.service.VgopServiceApplication [||||] [] - Running with Spring Boot v2.5.5, Spring v5.3.19
2025-07-17 12:37:49.160 [restartedMain] INFO  c.v.service.VgopServiceApplication [||||] [] - The following profiles are active: dev
2025-07-17 12:37:49.943 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\DataExportMapper.class]
2025-07-17 12:37:49.944 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\RevisionMapper.class]
2025-07-17 12:37:49.944 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\ValidationAlertsMapper.class]
2025-07-17 12:37:49.944 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\primary\RevisionTimesMapper.class]
2025-07-17 12:37:49.944 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\secondary\TaskExecutionMapper.class]
2025-07-17 12:37:49.944 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'dataExportMapper' and 'com.vgop.service.dao.DataExportMapper' mapperInterface
2025-07-17 12:37:49.944 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'revisionMapper' and 'com.vgop.service.dao.RevisionMapper' mapperInterface
2025-07-17 12:37:49.944 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'validationAlertsMapper' and 'com.vgop.service.dao.ValidationAlertsMapper' mapperInterface
2025-07-17 12:37:49.944 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'revisionTimesMapper' and 'com.vgop.service.dao.primary.RevisionTimesMapper' mapperInterface
2025-07-17 12:37:49.944 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'taskExecutionMapper' and 'com.vgop.service.dao.secondary.TaskExecutionMapper' mapperInterface
2025-07-17 12:37:49.987 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [||||] [] - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-17 12:37:50.328 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer [||||] [] - Tomcat initialized with port(s): 8080 (http)
2025-07-17 12:37:50.328 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol [||||] [] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-17 12:37:50.328 [restartedMain] INFO  o.a.catalina.core.StandardService [||||] [] - Starting service [Tomcat]
2025-07-17 12:37:50.329 [restartedMain] INFO  o.a.catalina.core.StandardEngine [||||] [] - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-17 12:37:50.392 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/vgop] [||||] [] - Initializing Spring embedded WebApplicationContext
2025-07-17 12:37:50.393 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext [||||] [] - Root WebApplicationContext: initialization completed in 1229 ms
2025-07-17 12:37:50.464 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - ValidationEngine已初始化，等待规则配置...
2025-07-17 12:37:51.427 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\mapper\DataExportMapper.xml]'
2025-07-17 12:37:51.434 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\mapper\RevisionMapper.xml]'
2025-07-17 12:37:51.441 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\mapper\TaskExecutionMapper.xml]'
2025-07-17 12:37:51.451 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\mapper\ValidationAlertsMapper.xml]'
2025-07-17 12:37:51.538 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext [||||] [] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'vgopTaskController' defined in file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\controller\VgopTaskController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.vgop.service.service.VgopTaskScheduler' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-07-17 12:37:51.538 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-0} closing ...
2025-07-17 12:37:51.539 [restartedMain] INFO  o.a.catalina.core.StandardService [||||] [] - Stopping service [Tomcat]
2025-07-17 12:37:51.550 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener [||||] [] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-17 12:37:51.589 [restartedMain] ERROR o.s.b.d.LoggingFailureAnalysisReporter [||||] [] - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.vgop.service.controller.VgopTaskController required a bean of type 'com.vgop.service.service.VgopTaskScheduler' that could not be found.


Action:

Consider defining a bean of type 'com.vgop.service.service.VgopTaskScheduler' in your configuration.

2025-07-17 12:37:53.383 [restartedMain] INFO  c.v.service.VgopServiceApplication [||||] [] - Starting VgopServiceApplication using Java 1.8.0_452 on ziber with PID 25692 (C:\workspaces\hdh\vgop-vli\vgop-service\target\classes started by galil in C:\workspaces\hdh\vgop-vli\vgop-service)
2025-07-17 12:37:53.383 [restartedMain] DEBUG c.v.service.VgopServiceApplication [||||] [] - Running with Spring Boot v2.5.5, Spring v5.3.19
2025-07-17 12:37:53.383 [restartedMain] INFO  c.v.service.VgopServiceApplication [||||] [] - The following profiles are active: dev
2025-07-17 12:37:53.881 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\DataExportMapper.class]
2025-07-17 12:37:53.882 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\RevisionMapper.class]
2025-07-17 12:37:53.882 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\ValidationAlertsMapper.class]
2025-07-17 12:37:53.882 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\primary\RevisionTimesMapper.class]
2025-07-17 12:37:53.882 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\secondary\TaskExecutionMapper.class]
2025-07-17 12:37:53.882 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'dataExportMapper' and 'com.vgop.service.dao.DataExportMapper' mapperInterface
2025-07-17 12:37:53.882 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'revisionMapper' and 'com.vgop.service.dao.RevisionMapper' mapperInterface
2025-07-17 12:37:53.882 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'validationAlertsMapper' and 'com.vgop.service.dao.ValidationAlertsMapper' mapperInterface
2025-07-17 12:37:53.882 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'revisionTimesMapper' and 'com.vgop.service.dao.primary.RevisionTimesMapper' mapperInterface
2025-07-17 12:37:53.882 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'taskExecutionMapper' and 'com.vgop.service.dao.secondary.TaskExecutionMapper' mapperInterface
2025-07-17 12:37:53.908 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [||||] [] - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-17 12:37:53.973 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer [||||] [] - Tomcat initialized with port(s): 8080 (http)
2025-07-17 12:37:53.974 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol [||||] [] - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-17 12:37:53.974 [restartedMain] INFO  o.a.catalina.core.StandardService [||||] [] - Starting service [Tomcat]
2025-07-17 12:37:53.975 [restartedMain] INFO  o.a.catalina.core.StandardEngine [||||] [] - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-17 12:37:54.017 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/vgop] [||||] [] - Initializing Spring embedded WebApplicationContext
2025-07-17 12:37:54.017 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext [||||] [] - Root WebApplicationContext: initialization completed in 631 ms
2025-07-17 12:37:54.051 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - ValidationEngine已初始化，等待规则配置...
2025-07-17 12:37:54.918 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\mapper\DataExportMapper.xml]'
2025-07-17 12:37:54.924 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\mapper\RevisionMapper.xml]'
2025-07-17 12:37:54.930 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\mapper\TaskExecutionMapper.xml]'
2025-07-17 12:37:54.939 [restartedMain] DEBUG o.m.spring.SqlSessionFactoryBean [||||] [] - Parsed mapper file: 'file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\mapper\ValidationAlertsMapper.xml]'
2025-07-17 12:37:55.058 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - === 开始测试数据库连接 ===
2025-07-17 12:37:55.058 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 测试主数据源连接...
2025-07-17 12:37:55.058 [restartedMain] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - testWhileIdle is true, validationQuery not set
2025-07-17 12:37:55.060 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-9} inited
2025-07-17 12:37:55.920 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 主数据源连接成功: {databaseProductVersion=12.10.FC4G1AEE, databaseProductName=GBase Server, driverVersion=4.10.JC4G1N999, name=主数据源, driverName=GBase JDBC Driver for GBase Server, userName=ismp, url=jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;}
2025-07-17 12:37:55.920 [restartedMain] INFO  com.vgop.service.util.DatabaseUtil [||||] [] - 检测到数据库产品: GBase Server, URL: jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;
2025-07-17 12:37:55.920 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 检测到主数据库类型: gbase
2025-07-17 12:37:55.920 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 是否支持UNLOAD命令: true
2025-07-17 12:37:55.920 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 测试次数据源连接...
2025-07-17 12:37:55.921 [restartedMain] ERROR c.alibaba.druid.pool.DruidDataSource [||||] [] - testWhileIdle is true, validationQuery not set
2025-07-17 12:37:55.921 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-10} inited
2025-07-17 12:37:56.870 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - 次数据源连接成功: {databaseProductVersion=12.10.FC4G1AEE, databaseProductName=GBase Server, driverVersion=4.10.JC4G1N999, name=次数据源, driverName=GBase JDBC Driver for GBase Server, userName=ismp, url=jdbc:gbasedbt-sqli://***********:7777/bms:GBASEDBTSERVER=gb33207;DB_LOCALE=EN_US.8859-1;NEWCODESET=GBK,8859-1,819;}
2025-07-17 12:37:56.871 [restartedMain] INFO  c.v.s.service.DatabaseTestService [||||] [] - === 数据库连接测试完成 ===
2025-07-17 12:37:56.999 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory [||||] [] - Using default implementation for ThreadExecutor
2025-07-17 12:37:57.000 [restartedMain] INFO  o.quartz.core.SchedulerSignalerImpl [||||] [] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-17 12:37:57.000 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - Quartz Scheduler v.2.3.2 created.
2025-07-17 12:37:57.001 [restartedMain] INFO  org.quartz.simpl.RAMJobStore [||||] [] - RAMJobStore initialized.
2025-07-17 12:37:57.001 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-17 12:37:57.001 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory [||||] [] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-17 12:37:57.001 [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory [||||] [] - Quartz scheduler version: 2.3.2
2025-07-17 12:37:57.001 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@35c25fd6
2025-07-17 12:37:57.019 [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver [||||] [] - Exposing 15 endpoint(s) beneath base path '/actuator'
2025-07-17 12:37:57.051 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:37:57.052 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库产品名称: GBase Server, 版本: 12.10.FC4G1AEE
2025-07-17 12:37:57.052 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 检测到数据库类型: GBase Server
2025-07-17 12:37:57.052 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 配置GBase数据库字符编码设置
2025-07-17 12:37:57.052 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]
2025-07-17 12:37:57.052 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:37:57.118 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Looking up default SQLErrorCodes for DataSource [com.alibaba.druid.pool.DruidDataSource@1afe95b5]
2025-07-17 12:37:57.119 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:37:57.119 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - SQL error codes for 'GBase Server' not found
2025-07-17 12:37:57.119 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Caching SQL error codes for DataSource [com.alibaba.druid.pool.DruidDataSource@1afe95b5]: database product name is 'GBase Server'
2025-07-17 12:37:57.119 [restartedMain] DEBUG o.s.j.s.SQLErrorCodeSQLExceptionTranslator [||||] [] - Unable to translate SQLException with Error code '-728', will now try the fallback translator
2025-07-17 12:37:57.119 [restartedMain] DEBUG o.s.j.s.SQLStateSQLExceptionTranslator [||||] [] - Extracted SQL state class 'IX' from value 'IX000'
2025-07-17 12:37:57.119 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 查询GBase数据库字符编码信息失败: StatementCallback; uncategorized SQLException for SQL [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]; SQL state [IX000]; error code [-728]; Unknown first argument of dbinfo(dblocale).; nested exception is java.sql.SQLException: Unknown first argument of dbinfo(dblocale).
2025-07-17 12:37:57.119 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - GBase数据库字符编码配置完成，依赖JDBC URL参数: DB_LOCALE, NEWCODESET
2025-07-17 12:37:57.119 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dbhostname') FROM systables WHERE tabid = 1]
2025-07-17 12:37:57.119 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:37:57.731 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库主机名: eb33207
2025-07-17 12:37:57.731 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT LENGTH('测试中文字符') FROM systables WHERE tabid = 1]
2025-07-17 12:37:57.731 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:37:58.020 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 字符编码兼容性测试通过，中文字符长度: 12
2025-07-17 12:37:58.021 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 已配置主数据源字符编码安全的JdbcTemplate
2025-07-17 12:37:58.023 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:37:58.023 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库产品名称: GBase Server, 版本: 12.10.FC4G1AEE
2025-07-17 12:37:58.024 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 检测到数据库类型: GBase Server
2025-07-17 12:37:58.024 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 配置GBase数据库字符编码设置
2025-07-17 12:37:58.024 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]
2025-07-17 12:37:58.024 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:37:58.080 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Looking up default SQLErrorCodes for DataSource [com.alibaba.druid.pool.DruidDataSource@48539678]
2025-07-17 12:37:58.080 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:37:58.081 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - SQL error codes for 'GBase Server' not found
2025-07-17 12:37:58.081 [restartedMain] DEBUG o.s.j.support.SQLErrorCodesFactory [||||] [] - Caching SQL error codes for DataSource [com.alibaba.druid.pool.DruidDataSource@48539678]: database product name is 'GBase Server'
2025-07-17 12:37:58.081 [restartedMain] DEBUG o.s.j.s.SQLErrorCodeSQLExceptionTranslator [||||] [] - Unable to translate SQLException with Error code '-728', will now try the fallback translator
2025-07-17 12:37:58.081 [restartedMain] DEBUG o.s.j.s.SQLStateSQLExceptionTranslator [||||] [] - Extracted SQL state class 'IX' from value 'IX000'
2025-07-17 12:37:58.081 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 查询GBase数据库字符编码信息失败: StatementCallback; uncategorized SQLException for SQL [SELECT DBINFO('dblocale') FROM systables WHERE tabid = 1]; SQL state [IX000]; error code [-728]; Unknown first argument of dbinfo(dblocale).; nested exception is java.sql.SQLException: Unknown first argument of dbinfo(dblocale).
2025-07-17 12:37:58.081 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - GBase数据库字符编码配置完成，依赖JDBC URL参数: DB_LOCALE, NEWCODESET
2025-07-17 12:37:58.081 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT DBINFO('dbhostname') FROM systables WHERE tabid = 1]
2025-07-17 12:37:58.081 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:37:58.644 [restartedMain] DEBUG c.v.s.config.DatabaseCharsetConfig [||||] [] - 数据库主机名: eb33207
2025-07-17 12:37:58.644 [restartedMain] DEBUG o.s.jdbc.core.JdbcTemplate [||||] [] - Executing SQL query [SELECT LENGTH('测试中文字符') FROM systables WHERE tabid = 1]
2025-07-17 12:37:58.644 [restartedMain] DEBUG o.s.jdbc.datasource.DataSourceUtils [||||] [] - Fetching JDBC Connection from DataSource
2025-07-17 12:37:58.962 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 字符编码兼容性测试通过，中文字符长度: 12
2025-07-17 12:37:58.962 [restartedMain] INFO  c.v.s.config.DatabaseCharsetConfig [||||] [] - 已配置次数据源字符编码安全的JdbcTemplate
2025-07-17 12:37:59.007 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer [||||] [] - LiveReload server is running on port 35729
2025-07-17 12:37:59.100 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol [||||] [] - Starting ProtocolHandler ["http-nio-8080"]
2025-07-17 12:37:59.103 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer [||||] [] - Tomcat started on port(s): 8080 (http) with context path '/vgop'
2025-07-17 12:37:59.255 [restartedMain] INFO  o.s.s.quartz.SchedulerFactoryBean [||||] [] - Starting Quartz Scheduler now
2025-07-17 12:37:59.255 [restartedMain] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-17 12:37:59.263 [restartedMain] INFO  c.v.service.VgopServiceApplication [||||] [] - Started VgopServiceApplication in 6.023 seconds (JVM running for 1468.961)
2025-07-17 12:37:59.265 [restartedMain] INFO  c.v.s.s.ConfigValidationService [||||] [] - 开始验证应用配置...
2025-07-17 12:37:59.266 [restartedMain] INFO  c.v.s.s.ConfigValidationService [||||] [] - 配置验证通过
2025-07-17 12:37:59.266 [restartedMain] INFO  c.v.s.service.DirectoryInitService [||||] [] - 开始初始化目录结构...
2025-07-17 12:37:59.266 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 数据导出根目录 - ./VGOPdata/datafile/
2025-07-17 12:37:59.266 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日志根目录 - ./logs/
2025-07-17 12:37:59.266 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 备份根目录 - ./data/backup/
2025-07-17 12:37:59.267 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 告警文件目录 - ./data/alerts/
2025-07-17 12:37:59.267 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250714 - ./VGOPdata/datafile//20250714/day
2025-07-17 12:37:59.268 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250714 - ./VGOPdata/datafile//20250714/month
2025-07-17 12:37:59.268 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250715 - ./VGOPdata/datafile//20250715/day
2025-07-17 12:37:59.268 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250715 - ./VGOPdata/datafile//20250715/month
2025-07-17 12:37:59.269 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250716 - ./VGOPdata/datafile//20250716/day
2025-07-17 12:37:59.269 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250716 - ./VGOPdata/datafile//20250716/month
2025-07-17 12:37:59.269 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250717 - ./VGOPdata/datafile//20250717/day
2025-07-17 12:37:59.270 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250717 - ./VGOPdata/datafile//20250717/month
2025-07-17 12:37:59.270 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250718 - ./VGOPdata/datafile//20250718/day
2025-07-17 12:37:59.270 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250718 - ./VGOPdata/datafile//20250718/month
2025-07-17 12:37:59.271 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 日数据目录 - 20250719 - ./VGOPdata/datafile//20250719/day
2025-07-17 12:37:59.271 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月数据目录 - 20250719 - ./VGOPdata/datafile//20250719/month
2025-07-17 12:37:59.271 [restartedMain] DEBUG c.v.s.service.DirectoryInitService [||||] [] - 目录已存在: 月度数据目录 - 202507 - ./VGOPdata/datafile//202507
2025-07-17 12:37:59.271 [restartedMain] INFO  c.v.s.service.DirectoryInitService [||||] [] - 目录结构初始化完成
2025-07-17 12:37:59.273 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener [||||] [] - Condition evaluation unchanged
2025-07-17 12:37:59.273 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 开始从配置文件加载校验规则...
2025-07-17 12:37:59.273 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24201 的校验规则...
2025-07-17 12:37:59.273 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phonenumber.format - FORMAT
2025-07-17 12:37:59.274 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-17 12:37:59.274 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phonenumber 添加了规则: FORMAT
2025-07-17 12:37:59.274 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phonenumber.length - LENGTH
2025-07-17 12:37:59.274 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-17 12:37:59.274 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phonenumber 添加了规则: LENGTH
2025-07-17 12:37:59.274 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phonestate.enum - ENUM
2025-07-17 12:37:59.274 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phonestate, 规则ID=common.phonestate.enum, 规则名称=枚举值校验
2025-07-17 12:37:59.274 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phonestate 添加了规则: ENUM
2025-07-17 12:37:59.274 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phoneimsi.length - LENGTH
2025-07-17 12:37:59.274 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phoneimsi, 规则ID=common.phoneimsi.length, 规则名称=长度校验
2025-07-17 12:37:59.274 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phoneimsi 添加了规则: LENGTH
2025-07-17 12:37:59.274 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.phoneimei.length - LENGTH
2025-07-17 12:37:59.274 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=phoneimei, 规则ID=common.phoneimei.length, 规则名称=长度校验
2025-07-17 12:37:59.274 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 phoneimei 添加了规则: LENGTH
2025-07-17 12:37:59.274 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.locationid.length - LENGTH
2025-07-17 12:37:59.274 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=locationid, 规则ID=common.locationid.length, 规则名称=长度校验
2025-07-17 12:37:59.274 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 locationid 添加了规则: LENGTH
2025-07-17 12:37:59.274 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.provinceid.length - LENGTH
2025-07-17 12:37:59.274 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=provinceid, 规则ID=common.provinceid.length, 规则名称=长度校验
2025-07-17 12:37:59.275 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 provinceid 添加了规则: LENGTH
2025-07-17 12:37:59.275 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.openingtime.format - FORMAT
2025-07-17 12:37:59.275 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=openingtime, 规则ID=common.openingtime.format, 规则名称=格式校验
2025-07-17 12:37:59.275 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 openingtime 添加了规则: FORMAT
2025-07-17 12:37:59.275 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.optime.format - FORMAT
2025-07-17 12:37:59.275 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=Optime, 规则ID=common.Optime.format, 规则名称=格式校验
2025-07-17 12:37:59.275 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 Optime 添加了规则: FORMAT
2025-07-17 12:37:59.275 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24201.sex.enum - ENUM
2025-07-17 12:37:59.275 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24201, 字段=sex, 规则ID=common.sex.enum, 规则名称=枚举值校验
2025-07-17 12:37:59.275 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 字段 sex 添加了规则: ENUM
2025-07-17 12:37:59.275 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24201, 字段数量=9
2025-07-17 12:37:59.275 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 注册了 9 个字段的元数据
2025-07-17 12:37:59.275 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [phonestate, Optime, phoneimei, openingtime, locationid, sex, phonenumber, phoneimsi, provinceid] -> VGOP1-R2-10-24201
2025-07-17 12:37:59.275 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24201 注册了字段到接口的映射关系
2025-07-17 12:37:59.275 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24201 加载了 10 个字段校验规则
2025-07-17 12:37:59.275 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24202 的校验规则...
2025-07-17 12:37:59.275 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnumber.format - FORMAT
2025-07-17 12:37:59.275 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-17 12:37:59.275 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnumber 添加了规则: FORMAT
2025-07-17 12:37:59.275 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnumber.length - LENGTH
2025-07-17 12:37:59.275 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnumber, 规则ID=common.mcnnumber.length, 规则名称=长度校验
2025-07-17 12:37:59.275 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnumber 添加了规则: LENGTH
2025-07-17 12:37:59.275 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.phonenumber.format - FORMAT
2025-07-17 12:37:59.276 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-17 12:37:59.276 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 phonenumber 添加了规则: FORMAT
2025-07-17 12:37:59.276 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.phonenumber.length - LENGTH
2025-07-17 12:37:59.276 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-17 12:37:59.276 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 phonenumber 添加了规则: LENGTH
2025-07-17 12:37:59.276 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.business.enum - ENUM
2025-07-17 12:37:59.276 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=business, 规则ID=common.business.enum, 规则名称=枚举值校验
2025-07-17 12:37:59.276 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 business 添加了规则: ENUM
2025-07-17 12:37:59.276 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.shutdown.length - LENGTH
2025-07-17 12:37:59.276 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=shutdown, 规则ID=common.shutdown.length, 规则名称=长度校验
2025-07-17 12:37:59.276 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 shutdown 添加了规则: LENGTH
2025-07-17 12:37:59.276 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnimsi.length - LENGTH
2025-07-17 12:37:59.276 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnimsi, 规则ID=common.mcnimsi.length, 规则名称=长度校验
2025-07-17 12:37:59.276 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnimsi 添加了规则: LENGTH
2025-07-17 12:37:59.276 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnimsi.format - FORMAT
2025-07-17 12:37:59.276 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnimsi, 规则ID=common.mcnimsi.format, 规则名称=格式校验
2025-07-17 12:37:59.276 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnimsi 添加了规则: FORMAT
2025-07-17 12:37:59.276 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnlocationid.length - LENGTH
2025-07-17 12:37:59.276 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnlocationid, 规则ID=common.mcnlocationid.length, 规则名称=长度校验
2025-07-17 12:37:59.276 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnlocationid 添加了规则: LENGTH
2025-07-17 12:37:59.276 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.numstate.length - LENGTH
2025-07-17 12:37:59.276 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=numstate, 规则ID=common.numstate.length, 规则名称=长度校验
2025-07-17 12:37:59.276 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 numstate 添加了规则: LENGTH
2025-07-17 12:37:59.276 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnature.enum - ENUM
2025-07-17 12:37:59.276 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnature, 规则ID=common.mcnnature.enum, 规则名称=枚举值校验
2025-07-17 12:37:59.276 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnature 添加了规则: ENUM
2025-07-17 12:37:59.276 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnum.enum - ENUM
2025-07-17 12:37:59.276 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnum, 规则ID=common.mcnnum.enum, 规则名称=枚举值校验
2025-07-17 12:37:59.276 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnum 添加了规则: ENUM
2025-07-17 12:37:59.276 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcnnum.length - LENGTH
2025-07-17 12:37:59.277 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcnnum, 规则ID=common.mcnnum.length, 规则名称=长度校验
2025-07-17 12:37:59.277 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcnnum 添加了规则: LENGTH
2025-07-17 12:37:59.277 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.channel.length - LENGTH
2025-07-17 12:37:59.277 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=channel, 规则ID=common.channel.length, 规则名称=长度校验
2025-07-17 12:37:59.277 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 channel 添加了规则: LENGTH
2025-07-17 12:37:59.277 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mj.enum - ENUM
2025-07-17 12:37:59.277 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mj, 规则ID=common.mj.enum, 规则名称=枚举值校验
2025-07-17 12:37:59.277 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mj 添加了规则: ENUM
2025-07-17 12:37:59.277 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.openingtime.format - FORMAT
2025-07-17 12:37:59.277 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=openingtime, 规则ID=common.openingtime.format, 规则名称=格式校验
2025-07-17 12:37:59.277 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 openingtime 添加了规则: FORMAT
2025-07-17 12:37:59.277 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.optime.format - FORMAT
2025-07-17 12:37:59.277 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=Optime, 规则ID=common.Optime.format, 规则名称=格式校验
2025-07-17 12:37:59.277 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 Optime 添加了规则: FORMAT
2025-07-17 12:37:59.277 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.mcimsitime.format - FORMAT
2025-07-17 12:37:59.277 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=mcimsitime, 规则ID=common.mcimsitime.format, 规则名称=格式校验
2025-07-17 12:37:59.277 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 mcimsitime 添加了规则: FORMAT
2025-07-17 12:37:59.277 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.usertype.length - LENGTH
2025-07-17 12:37:59.277 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=usertype, 规则ID=common.usertype.length, 规则名称=长度校验
2025-07-17 12:37:59.277 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 usertype 添加了规则: LENGTH
2025-07-17 12:37:59.277 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.begintime.format - FORMAT
2025-07-17 12:37:59.277 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=Begintime, 规则ID=common.Begintime.format, 规则名称=格式校验
2025-07-17 12:37:59.277 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 Begintime 添加了规则: FORMAT
2025-07-17 12:37:59.277 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.endtime.format - FORMAT
2025-07-17 12:37:59.277 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=Endtime, 规则ID=common.Endtime.format, 规则名称=格式校验
2025-07-17 12:37:59.277 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 Endtime 添加了规则: FORMAT
2025-07-17 12:37:59.277 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24202.servid.length - LENGTH
2025-07-17 12:37:59.277 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24202, 字段=ServID, 规则ID=common.ServID.length, 规则名称=长度校验
2025-07-17 12:37:59.277 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 字段 ServID 添加了规则: LENGTH
2025-07-17 12:37:59.278 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24202, 字段数量=18
2025-07-17 12:37:59.278 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 注册了 18 个字段的元数据
2025-07-17 12:37:59.278 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [mcnlocationid, Optime, business, mcnimsi, mcimsitime, phonenumber, mcnnum, channel, usertype, numstate, Endtime, openingtime, ServID, mcnnature, Begintime, mcnnumber, mj, shutdown] -> VGOP1-R2-10-24202
2025-07-17 12:37:59.278 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24202 注册了字段到接口的映射关系
2025-07-17 12:37:59.278 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24202 加载了 22 个字段校验规则
2025-07-17 12:37:59.278 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24203 的校验规则...
2025-07-17 12:37:59.278 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.account_id.length - LENGTH
2025-07-17 12:37:59.278 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=account_id, 规则ID=common.account_id.length, 规则名称=长度校验
2025-07-17 12:37:59.278 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 account_id 添加了规则: LENGTH
2025-07-17 12:37:59.278 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.phonenumber.format - FORMAT
2025-07-17 12:37:59.278 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-17 12:37:59.278 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 phonenumber 添加了规则: FORMAT
2025-07-17 12:37:59.278 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.phonenumber.length - LENGTH
2025-07-17 12:37:59.278 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-17 12:37:59.278 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 phonenumber 添加了规则: LENGTH
2025-07-17 12:37:59.278 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.type.enum - ENUM
2025-07-17 12:37:59.278 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=type, 规则ID=common.type.enum, 规则名称=枚举值校验
2025-07-17 12:37:59.278 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 type 添加了规则: ENUM
2025-07-17 12:37:59.278 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.optime.format - FORMAT
2025-07-17 12:37:59.278 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=optime, 规则ID=common.optime.format, 规则名称=格式校验
2025-07-17 12:37:59.278 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 optime 添加了规则: FORMAT
2025-07-17 12:37:59.278 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24203.version.length - LENGTH
2025-07-17 12:37:59.278 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24203, 字段=version, 规则ID=common.version.length, 规则名称=长度校验
2025-07-17 12:37:59.278 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 字段 version 添加了规则: LENGTH
2025-07-17 12:37:59.278 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24203, 字段数量=5
2025-07-17 12:37:59.279 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 注册了 5 个字段的元数据
2025-07-17 12:37:59.279 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [account_id, optime, phonenumber, type, version] -> VGOP1-R2-10-24203
2025-07-17 12:37:59.279 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24203 注册了字段到接口的映射关系
2025-07-17 12:37:59.279 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24203 加载了 6 个字段校验规则
2025-07-17 12:37:59.279 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24205 的校验规则...
2025-07-17 12:37:59.279 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.calltype.enum - ENUM
2025-07-17 12:37:59.279 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=callType, 规则ID=common.callType.enum, 规则名称=枚举值校验
2025-07-17 12:37:59.279 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 callType 添加了规则: ENUM
2025-07-17 12:37:59.279 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callingpartynumber.format - FORMAT
2025-07-17 12:37:59.279 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=callingPartyNumber, 规则ID=common.callingPartyNumber.format, 规则名称=格式校验
2025-07-17 12:37:59.279 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 callingPartyNumber 添加了规则: FORMAT
2025-07-17 12:37:59.279 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callingpartynumber.length - LENGTH
2025-07-17 12:37:59.279 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=callingPartyNumber, 规则ID=common.callingPartyNumber.length, 规则名称=长度校验
2025-07-17 12:37:59.279 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 callingPartyNumber 添加了规则: LENGTH
2025-07-17 12:37:59.279 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.calledpartynumber.format - FORMAT
2025-07-17 12:37:59.279 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=calledPartyNumber, 规则ID=common.calledPartyNumber.format, 规则名称=格式校验
2025-07-17 12:37:59.279 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 calledPartyNumber 添加了规则: FORMAT
2025-07-17 12:37:59.279 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.calledpartynumber.length - LENGTH
2025-07-17 12:37:59.279 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=calledPartyNumber, 规则ID=common.calledPartyNumber.length, 规则名称=长度校验
2025-07-17 12:37:59.279 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 calledPartyNumber 添加了规则: LENGTH
2025-07-17 12:37:59.279 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.mcnnumber.format - FORMAT
2025-07-17 12:37:59.279 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-17 12:37:59.279 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 mcnnumber 添加了规则: FORMAT
2025-07-17 12:37:59.279 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callbegintime.format - FORMAT
2025-07-17 12:37:59.279 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallBeginTime, 规则ID=common.CallBeginTime.format, 规则名称=格式校验
2025-07-17 12:37:59.279 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallBeginTime 添加了规则: FORMAT
2025-07-17 12:37:59.279 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callendtime.format - FORMAT
2025-07-17 12:37:59.279 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallEndTime, 规则ID=common.CallEndTime.format, 规则名称=格式校验
2025-07-17 12:37:59.279 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallEndTime 添加了规则: FORMAT
2025-07-17 12:37:59.279 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callduration.format - FORMAT
2025-07-17 12:37:59.280 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallDuration, 规则ID=common.CallDuration.format, 规则名称=格式校验
2025-07-17 12:37:59.280 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallDuration 添加了规则: FORMAT
2025-07-17 12:37:59.280 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24205.callduration.range - RANGE
2025-07-17 12:37:59.280 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24205, 字段=CallDuration, 规则ID=common.CallDuration.range, 规则名称=范围校验
2025-07-17 12:37:59.280 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 字段 CallDuration 添加了规则: RANGE
2025-07-17 12:37:59.280 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24205, 字段数量=7
2025-07-17 12:37:59.280 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 注册了 7 个字段的元数据
2025-07-17 12:37:59.280 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [CallEndTime, callingPartyNumber, calledPartyNumber, CallBeginTime, mcnnumber, CallDuration, callType] -> VGOP1-R2-10-24205
2025-07-17 12:37:59.280 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24205 注册了字段到接口的映射关系
2025-07-17 12:37:59.280 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24205 加载了 10 个字段校验规则
2025-07-17 12:37:59.280 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24206 的校验规则...
2025-07-17 12:37:59.280 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.chargetype.enum - ENUM
2025-07-17 12:37:59.280 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=chargetype, 规则ID=common.chargetype.enum, 规则名称=枚举值校验
2025-07-17 12:37:59.280 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 chargetype 添加了规则: ENUM
2025-07-17 12:37:59.280 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.phonenumber.format - FORMAT
2025-07-17 12:37:59.280 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-17 12:37:59.280 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 phonenumber 添加了规则: FORMAT
2025-07-17 12:37:59.280 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.phonenumber.length - LENGTH
2025-07-17 12:37:59.280 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-17 12:37:59.280 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 phonenumber 添加了规则: LENGTH
2025-07-17 12:37:59.280 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.mcnnumber.format - FORMAT
2025-07-17 12:37:59.280 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-17 12:37:59.280 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 mcnnumber 添加了规则: FORMAT
2025-07-17 12:37:59.280 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.sendorrcenum.format - FORMAT
2025-07-17 12:37:59.280 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=sendorreceNum, 规则ID=common.sendorreceNum.format, 规则名称=格式校验
2025-07-17 12:37:59.280 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 sendorreceNum 添加了规则: FORMAT
2025-07-17 12:37:59.280 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.sendorrcenum.length - LENGTH
2025-07-17 12:37:59.280 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=sendorreceNum, 规则ID=common.sendorreceNum.length, 规则名称=长度校验
2025-07-17 12:37:59.281 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 sendorreceNum 添加了规则: LENGTH
2025-07-17 12:37:59.281 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24206.optime.format - FORMAT
2025-07-17 12:37:59.281 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24206, 字段=optime, 规则ID=common.optime.format, 规则名称=格式校验
2025-07-17 12:37:59.281 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 字段 optime 添加了规则: FORMAT
2025-07-17 12:37:59.281 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24206, 字段数量=5
2025-07-17 12:37:59.281 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 注册了 5 个字段的元数据
2025-07-17 12:37:59.281 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [optime, phonenumber, chargetype, mcnnumber, sendorreceNum] -> VGOP1-R2-10-24206
2025-07-17 12:37:59.281 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24206 注册了字段到接口的映射关系
2025-07-17 12:37:59.281 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24206 加载了 7 个字段校验规则
2025-07-17 12:37:59.281 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-10-24207 的校验规则...
2025-07-17 12:37:59.281 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.phonenumber.format - FORMAT
2025-07-17 12:37:59.281 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=phonenumber, 规则ID=common.phonenumber.format, 规则名称=格式校验
2025-07-17 12:37:59.281 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 phonenumber 添加了规则: FORMAT
2025-07-17 12:37:59.281 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.phonenumber.length - LENGTH
2025-07-17 12:37:59.281 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=phonenumber, 规则ID=common.phonenumber.length, 规则名称=长度校验
2025-07-17 12:37:59.281 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 phonenumber 添加了规则: LENGTH
2025-07-17 12:37:59.281 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnnumber.format - FORMAT
2025-07-17 12:37:59.281 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnnumber, 规则ID=common.mcnnumber.format, 规则名称=格式校验
2025-07-17 12:37:59.281 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnnumber 添加了规则: FORMAT
2025-07-17 12:37:59.281 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnnumber.length - LENGTH
2025-07-17 12:37:59.281 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnnumber, 规则ID=common.mcnnumber.length, 规则名称=长度校验
2025-07-17 12:37:59.281 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnnumber 添加了规则: LENGTH
2025-07-17 12:37:59.281 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnimsi.format - FORMAT
2025-07-17 12:37:59.281 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnImsi, 规则ID=common.mcnImsi.format, 规则名称=格式校验
2025-07-17 12:37:59.281 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnImsi 添加了规则: FORMAT
2025-07-17 12:37:59.281 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.mcnimsi.length - LENGTH
2025-07-17 12:37:59.281 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=mcnImsi, 规则ID=common.mcnImsi.length, 规则名称=长度校验
2025-07-17 12:37:59.281 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 mcnImsi 添加了规则: LENGTH
2025-07-17 12:37:59.281 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.businessstate.enum - ENUM
2025-07-17 12:37:59.281 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=businessState, 规则ID=common.businessState.enum, 规则名称=枚举值校验
2025-07-17 12:37:59.281 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 businessState 添加了规则: ENUM
2025-07-17 12:37:59.282 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.numstate.format - FORMAT
2025-07-17 12:37:59.282 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=Numstate, 规则ID=common.Numstate.format, 规则名称=格式校验
2025-07-17 12:37:59.282 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 Numstate 添加了规则: FORMAT
2025-07-17 12:37:59.282 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.locationid.length - LENGTH
2025-07-17 12:37:59.282 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=Locationid, 规则ID=common.Locationid.length, 规则名称=长度校验
2025-07-17 12:37:59.282 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 Locationid 添加了规则: LENGTH
2025-07-17 12:37:59.282 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.bossprovinceid.format - FORMAT
2025-07-17 12:37:59.282 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=BossProvinceid, 规则ID=common.BossProvinceid.format, 规则名称=格式校验
2025-07-17 12:37:59.282 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 BossProvinceid 添加了规则: FORMAT
2025-07-17 12:37:59.282 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.bossprovinceid.range - RANGE
2025-07-17 12:37:59.282 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=BossProvinceid, 规则ID=common.BossProvinceid.range, 规则名称=范围校验
2025-07-17 12:37:59.282 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 BossProvinceid 添加了规则: RANGE
2025-07-17 12:37:59.282 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.10-24207.openingtime.format - FORMAT
2025-07-17 12:37:59.282 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-10-24207, 字段=openingtime, 规则ID=common.openingtime.format, 规则名称=格式校验
2025-07-17 12:37:59.282 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 字段 openingtime 添加了规则: FORMAT
2025-07-17 12:37:59.282 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-10-24207, 字段数量=8
2025-07-17 12:37:59.282 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 注册了 8 个字段的元数据
2025-07-17 12:37:59.282 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [Numstate, openingtime, mcnImsi, businessState, BossProvinceid, phonenumber, Locationid, mcnnumber] -> VGOP1-R2-10-24207
2025-07-17 12:37:59.282 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-10-24207 注册了字段到接口的映射关系
2025-07-17 12:37:59.282 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-10-24207 加载了 12 个字段校验规则
2025-07-17 12:37:59.282 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-11-24101 的校验规则...
2025-07-17 12:37:59.282 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.phonenum.format - FORMAT
2025-07-17 12:37:59.282 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=phonenum, 规则ID=common.phonenum.format, 规则名称=格式校验
2025-07-17 12:37:59.282 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 phonenum 添加了规则: FORMAT
2025-07-17 12:37:59.282 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.phonenum.range - RANGE
2025-07-17 12:37:59.282 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=phonenum, 规则ID=common.phonenum.range, 规则名称=范围校验
2025-07-17 12:37:59.282 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 phonenum 添加了规则: RANGE
2025-07-17 12:37:59.282 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnnum.format - FORMAT
2025-07-17 12:37:59.282 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnnum, 规则ID=common.mcnnum.format, 规则名称=格式校验
2025-07-17 12:37:59.283 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnnum 添加了规则: FORMAT
2025-07-17 12:37:59.283 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnnum.range - RANGE
2025-07-17 12:37:59.283 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnnum, 规则ID=common.mcnnum.range, 规则名称=范围校验
2025-07-17 12:37:59.283 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnnum 添加了规则: RANGE
2025-07-17 12:37:59.283 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.appactivenum.format - FORMAT
2025-07-17 12:37:59.283 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=appactivenum, 规则ID=common.appactivenum.format, 规则名称=格式校验
2025-07-17 12:37:59.283 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 appactivenum 添加了规则: FORMAT
2025-07-17 12:37:59.283 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.appactivenum.range - RANGE
2025-07-17 12:37:59.283 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=appactivenum, 规则ID=common.appactivenum.range, 规则名称=范围校验
2025-07-17 12:37:59.283 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 appactivenum 添加了规则: RANGE
2025-07-17 12:37:59.283 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnactivenum.format - FORMAT
2025-07-17 12:37:59.283 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnactivenum, 规则ID=common.mcnactivenum.format, 规则名称=格式校验
2025-07-17 12:37:59.283 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnactivenum 添加了规则: FORMAT
2025-07-17 12:37:59.283 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.mcnactivenum.range - RANGE
2025-07-17 12:37:59.283 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=mcnactivenum, 规则ID=common.mcnactivenum.range, 规则名称=范围校验
2025-07-17 12:37:59.283 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 mcnactivenum 添加了规则: RANGE
2025-07-17 12:37:59.283 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.paynum.format - FORMAT
2025-07-17 12:37:59.283 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=paynum, 规则ID=common.paynum.format, 规则名称=格式校验
2025-07-17 12:37:59.283 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 paynum 添加了规则: FORMAT
2025-07-17 12:37:59.283 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.paynum.range - RANGE
2025-07-17 12:37:59.283 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=paynum, 规则ID=common.paynum.range, 规则名称=范围校验
2025-07-17 12:37:59.283 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 paynum 添加了规则: RANGE
2025-07-17 12:37:59.283 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.feenum.format - FORMAT
2025-07-17 12:37:59.283 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=feenum, 规则ID=common.feenum.format, 规则名称=格式校验
2025-07-17 12:37:59.283 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 feenum 添加了规则: FORMAT
2025-07-17 12:37:59.283 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.feenum.range - RANGE
2025-07-17 12:37:59.283 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=feenum, 规则ID=common.feenum.range, 规则名称=范围校验
2025-07-17 12:37:59.283 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 feenum 添加了规则: RANGE
2025-07-17 12:37:59.283 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secphonenum.format - FORMAT
2025-07-17 12:37:59.283 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secphonenum, 规则ID=common.secphonenum.format, 规则名称=格式校验
2025-07-17 12:37:59.283 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secphonenum 添加了规则: FORMAT
2025-07-17 12:37:59.283 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secphonenum.range - RANGE
2025-07-17 12:37:59.284 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secphonenum, 规则ID=common.secphonenum.range, 规则名称=范围校验
2025-07-17 12:37:59.284 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secphonenum 添加了规则: RANGE
2025-07-17 12:37:59.284 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secmcnnum.format - FORMAT
2025-07-17 12:37:59.284 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secmcnnum, 规则ID=common.secmcnnum.format, 规则名称=格式校验
2025-07-17 12:37:59.284 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secmcnnum 添加了规则: FORMAT
2025-07-17 12:37:59.284 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.secmcnnum.range - RANGE
2025-07-17 12:37:59.284 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=secmcnnum, 规则ID=common.secmcnnum.range, 规则名称=范围校验
2025-07-17 12:37:59.284 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 secmcnnum 添加了规则: RANGE
2025-07-17 12:37:59.284 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.amcnnum.format - FORMAT
2025-07-17 12:37:59.284 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=amcnnum, 规则ID=common.amcnnum.format, 规则名称=格式校验
2025-07-17 12:37:59.284 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 amcnnum 添加了规则: FORMAT
2025-07-17 12:37:59.284 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.11-24101.amcnnum.range - RANGE
2025-07-17 12:37:59.284 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-11-24101, 字段=amcnnum, 规则ID=common.amcnnum.range, 规则名称=范围校验
2025-07-17 12:37:59.284 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 字段 amcnnum 添加了规则: RANGE
2025-07-17 12:37:59.284 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-11-24101, 字段数量=9
2025-07-17 12:37:59.284 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 注册了 9 个字段的元数据
2025-07-17 12:37:59.284 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [appactivenum, paynum, mcnnum, secphonenum, phonenum, mcnactivenum, secmcnnum, amcnnum, feenum] -> VGOP1-R2-11-24101
2025-07-17 12:37:59.284 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-11-24101 注册了字段到接口的映射关系
2025-07-17 12:37:59.284 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-11-24101 加载了 18 个字段校验规则
2025-07-17 12:37:59.284 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-13-24301 的校验规则...
2025-07-17 12:37:59.284 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servid.format - FORMAT
2025-07-17 12:37:59.284 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServID, 规则ID=common.ServID.format, 规则名称=格式校验
2025-07-17 12:37:59.284 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServID 添加了规则: FORMAT
2025-07-17 12:37:59.284 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servid.length - LENGTH
2025-07-17 12:37:59.284 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServID, 规则ID=common.ServID.length, 规则名称=长度校验
2025-07-17 12:37:59.284 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServID 添加了规则: LENGTH
2025-07-17 12:37:59.284 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servid.standard_format - LENGTH
2025-07-17 12:37:59.284 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServID, 规则ID=common.ServID.length, 规则名称=长度校验
2025-07-17 12:37:59.284 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServID 添加了规则: LENGTH
2025-07-17 12:37:59.285 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servname.length - LENGTH
2025-07-17 12:37:59.285 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServName, 规则ID=common.ServName.length, 规则名称=长度校验
2025-07-17 12:37:59.285 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServName 添加了规则: LENGTH
2025-07-17 12:37:59.285 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24301.servname.content - FORMAT
2025-07-17 12:37:59.285 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24301, 字段=ServName, 规则ID=common.ServName.format, 规则名称=格式校验
2025-07-17 12:37:59.285 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 字段 ServName 添加了规则: FORMAT
2025-07-17 12:37:59.285 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-13-24301, 字段数量=2
2025-07-17 12:37:59.285 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 注册了 2 个字段的元数据
2025-07-17 12:37:59.285 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [ServName, ServID] -> VGOP1-R2-13-24301
2025-07-17 12:37:59.285 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24301 注册了字段到接口的映射关系
2025-07-17 12:37:59.285 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-13-24301 加载了 5 个字段校验规则
2025-07-17 12:37:59.285 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-13-24302 的校验规则...
2025-07-17 12:37:59.285 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelcode.format - FORMAT
2025-07-17 12:37:59.285 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelcode, 规则ID=common.channelcode.format, 规则名称=格式校验
2025-07-17 12:37:59.285 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelcode 添加了规则: FORMAT
2025-07-17 12:37:59.285 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelcode.length - LENGTH
2025-07-17 12:37:59.285 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelcode, 规则ID=common.channelcode.length, 规则名称=长度校验
2025-07-17 12:37:59.285 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelcode 添加了规则: LENGTH
2025-07-17 12:37:59.285 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelcode.range - RANGE
2025-07-17 12:37:59.285 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelcode, 规则ID=common.channelcode.range, 规则名称=范围校验
2025-07-17 12:37:59.285 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelcode 添加了规则: RANGE
2025-07-17 12:37:59.285 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelname.length - LENGTH
2025-07-17 12:37:59.285 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelname, 规则ID=common.channelname.length, 规则名称=长度校验
2025-07-17 12:37:59.285 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelname 添加了规则: LENGTH
2025-07-17 12:37:59.285 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24302.channelname.content - FORMAT
2025-07-17 12:37:59.285 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24302, 字段=channelname, 规则ID=common.channelname.format, 规则名称=格式校验
2025-07-17 12:37:59.285 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 字段 channelname 添加了规则: FORMAT
2025-07-17 12:37:59.286 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-13-24302, 字段数量=2
2025-07-17 12:37:59.286 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 注册了 2 个字段的元数据
2025-07-17 12:37:59.286 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [channelname, channelcode] -> VGOP1-R2-13-24302
2025-07-17 12:37:59.286 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24302 注册了字段到接口的映射关系
2025-07-17 12:37:59.286 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-13-24302 加载了 5 个字段校验规则
2025-07-17 12:37:59.286 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 正在加载接口 VGOP1-R2-13-24303 的校验规则...
2025-07-17 12:37:59.286 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdown.format - FORMAT
2025-07-17 12:37:59.286 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdown, 规则ID=common.shutdown.format, 规则名称=格式校验
2025-07-17 12:37:59.286 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdown 添加了规则: FORMAT
2025-07-17 12:37:59.286 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdown.length - LENGTH
2025-07-17 12:37:59.286 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdown, 规则ID=common.shutdown.length, 规则名称=长度校验
2025-07-17 12:37:59.286 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdown 添加了规则: LENGTH
2025-07-17 12:37:59.286 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdown.range - RANGE
2025-07-17 12:37:59.286 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdown, 规则ID=common.shutdown.range, 规则名称=范围校验
2025-07-17 12:37:59.286 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdown 添加了规则: RANGE
2025-07-17 12:37:59.286 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdownname.length - LENGTH
2025-07-17 12:37:59.286 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdownname, 规则ID=common.shutdownname.length, 规则名称=长度校验
2025-07-17 12:37:59.286 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdownname 添加了规则: LENGTH
2025-07-17 12:37:59.286 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 成功创建规则实例: VGOP1-R2.13-24303.shutdownname.content - FORMAT
2025-07-17 12:37:59.286 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 加载校验规则: 接口=VGOP1-R2-13-24303, 字段=shutdownname, 规则ID=common.shutdownname.format, 规则名称=格式校验
2025-07-17 12:37:59.286 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 字段 shutdownname 添加了规则: FORMAT
2025-07-17 12:37:59.286 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - 注册字段元数据: 接口=VGOP1-R2-13-24303, 字段数量=2
2025-07-17 12:37:59.286 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 注册了 2 个字段的元数据
2025-07-17 12:37:59.286 [restartedMain] DEBUG c.v.s.validation.ValidationEngine [||||] [] - 注册字段到接口映射: [shutdownname, shutdown] -> VGOP1-R2-13-24303
2025-07-17 12:37:59.286 [restartedMain] DEBUG c.v.s.config.ValidationRuleConfig [||||] [] - 为接口 VGOP1-R2-13-24303 注册了字段到接口的映射关系
2025-07-17 12:37:59.286 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 接口 VGOP1-R2-13-24303 加载了 5 个字段校验规则
2025-07-17 12:37:59.286 [restartedMain] INFO  c.v.s.config.ValidationRuleConfig [||||] [] - 校验规则加载完成，共加载 100 个规则
2025-07-17 14:28:56.287 [Thread-573] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-17 14:28:58.833 [Thread-573] INFO  o.s.s.quartz.SchedulerFactoryBean [||||] [] - Shutting down Quartz Scheduler
2025-07-17 14:28:58.834 [Thread-573] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-17 14:28:58.834 [Thread-573] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-17 14:28:58.834 [Thread-573] INFO  org.quartz.core.QuartzScheduler [||||] [] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-17 14:28:58.837 [Thread-573] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-10} closing ...
2025-07-17 14:28:58.838 [Thread-573] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-10} closed
2025-07-17 14:28:58.841 [Thread-573] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-9} closing ...
2025-07-17 14:28:58.842 [Thread-573] INFO  c.alibaba.druid.pool.DruidDataSource [||||] [] - {dataSource-9} closed
2025-07-17 14:29:31.014 [restartedMain] INFO  c.v.service.VgopServiceApplication [||||] [] - Starting VgopServiceApplication using Java 1.8.0_452 on ziber with PID 25692 (C:\workspaces\hdh\vgop-vli\vgop-service\target\classes started by galil in C:\workspaces\hdh\vgop-vli\vgop-service)
2025-07-17 14:29:31.015 [restartedMain] DEBUG c.v.service.VgopServiceApplication [||||] [] - Running with Spring Boot v2.5.5, Spring v5.3.19
2025-07-17 14:29:31.015 [restartedMain] INFO  c.v.service.VgopServiceApplication [||||] [] - The following profiles are active: dev
2025-07-17 14:29:39.039 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\DataExportMapper.class]
2025-07-17 14:29:39.039 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\RevisionMapper.class]
2025-07-17 14:29:39.039 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\ValidationAlertsMapper.class]
2025-07-17 14:29:39.039 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\primary\RevisionTimesMapper.class]
2025-07-17 14:29:39.039 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Identified candidate component class: file [C:\workspaces\hdh\vgop-vli\vgop-service\target\classes\com\vgop\service\dao\secondary\TaskExecutionMapper.class]
2025-07-17 14:29:39.040 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'dataExportMapper' and 'com.vgop.service.dao.DataExportMapper' mapperInterface
2025-07-17 14:29:39.040 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'revisionMapper' and 'com.vgop.service.dao.RevisionMapper' mapperInterface
2025-07-17 14:29:39.040 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'validationAlertsMapper' and 'com.vgop.service.dao.ValidationAlertsMapper' mapperInterface
2025-07-17 14:29:39.041 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'revisionTimesMapper' and 'com.vgop.service.dao.primary.RevisionTimesMapper' mapperInterface
2025-07-17 14:29:39.042 [restartedMain] DEBUG o.m.s.mapper.ClassPathMapperScanner [||||] [] - Creating MapperFactoryBean with name 'taskExecutionMapper' and 'com.vgop.service.dao.secondary.TaskExecutionMapper' mapperInterface
2025-07-17 14:29:39.145 [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [||||] [] - Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-17 14:29:39.518 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer [||||] [] - Tomcat initialized with port(s): 8080 (http)
2025-07-17 14:29:39.519 [restartedMain] INFO  o.a.catalina.core.StandardService [||||] [] - Starting service [Tomcat]
2025-07-17 14:29:39.520 [restartedMain] INFO  o.a.catalina.core.StandardEngine [||||] [] - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-17 14:29:39.687 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/vgop] [||||] [] - Initializing Spring embedded WebApplicationContext
2025-07-17 14:29:39.687 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext [||||] [] - Root WebApplicationContext: initialization completed in 8646 ms
2025-07-17 14:29:39.856 [restartedMain] INFO  c.v.s.validation.ValidationEngine [||||] [] - ValidationEngine已初始化，等待规则配置...
