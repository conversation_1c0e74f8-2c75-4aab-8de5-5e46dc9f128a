-- 数据库表结构迁移脚本：为vgop_validation_alerts表添加批次字段
-- 文件：add_batch_id_to_validation_alerts.sql
-- 说明：新增batch_id字段用于区分不同批次的数据质量告警记录

-- ====================
-- 1. 为vgop_validation_alerts表添加batch_id字段
-- ====================

-- H2数据库版本（用于测试环境）
-- ALTER TABLE vgop_validation_alerts ADD COLUMN batch_id VARCHAR(50);

-- Informix/GBase数据库版本（用于生产环境）
ALTER TABLE vgop_validation_alerts ADD COLUMN batch_id VARCHAR(50);

-- ====================
-- 2. 为batch_id字段创建索引以提高查询性能
-- ====================

-- 创建批次ID索引
CREATE INDEX idx_alerts_batch_id ON vgop_validation_alerts(batch_id);

-- 创建组合索引：接口名称 + 批次ID（用于批次级别的数据隔离查询）
CREATE INDEX idx_alerts_interface_batch ON vgop_validation_alerts(interface_name, batch_id);

-- 创建组合索引：数据日期 + 批次ID（用于按日期和批次查询）
CREATE INDEX idx_alerts_date_batch ON vgop_validation_alerts(SUBSTR(alert_time, 1, 8), batch_id);

-- ====================
-- 3. 验证字段添加结果
-- ====================

-- 检查表结构（Informix语法）
-- SELECT colname, coltype, collength 
-- FROM syscolumns 
-- WHERE tabid = (SELECT tabid FROM systables WHERE tabname = 'vgop_validation_alerts')
-- ORDER BY colno;

-- 检查索引创建结果
-- SELECT idxname, idxtype FROM sysindexes 
-- WHERE tabid = (SELECT tabid FROM systables WHERE tabname = 'vgop_validation_alerts');

-- ====================
-- 4. 数据迁移说明
-- ====================

-- 注意：现有记录的batch_id字段将为NULL
-- 这是预期的行为，因为历史记录没有批次概念
-- 新的告警记录将在应用程序中自动填入batch_id值

-- 如果需要为历史记录补充batch_id，可以使用以下语句：
-- UPDATE vgop_validation_alerts 
-- SET batch_id = CONCAT('legacy-', SUBSTR(alert_time, 1, 8))
-- WHERE batch_id IS NULL;

-- ====================
-- 5. 回滚脚本（如果需要）
-- ====================

-- 如果需要回滚此次修改，可以执行以下语句：
-- DROP INDEX IF EXISTS idx_alerts_batch_id;
-- DROP INDEX IF EXISTS idx_alerts_interface_batch;
-- DROP INDEX IF EXISTS idx_alerts_date_batch;
-- ALTER TABLE vgop_validation_alerts DROP COLUMN batch_id; 